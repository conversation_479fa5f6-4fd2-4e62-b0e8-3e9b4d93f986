{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://just4dev.dvc.ibank2dev.com", "description": "Generated server url"}], "paths": {"/gov/txn/reject/1.0": {"post": {"tags": ["governmentService"], "summary": "Reject transaction", "description": "Reject transaction", "operationId": "reject", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnRejectReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnRejectRes"}}}}}}}, "/gov/txn/push/1.0": {"post": {"tags": ["governmentService"], "summary": "Init push transaction", "description": "Init push transaction", "operationId": "initPush", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnInitPushReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnInitPushRes"}}}}}}}, "/gov/txn/print/1.0": {"post": {"tags": ["governmentService"], "summary": "Print document", "description": "Print document", "operationId": "print", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnPrintDocumentReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExportFileRes"}}}}}}}, "/gov/txn/pending-approval/list/1.0": {"post": {"tags": ["governmentService"], "summary": "List pending approval transaction", "description": "List pending approval transaction", "operationId": "listPendingApproval", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnPendingApprovalListReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataListTxnPendingApprovalListRes"}}}}}}}, "/gov/txn/export/1.0": {"post": {"tags": ["governmentService"], "summary": "Export report transaction", "description": "Export report transaction", "operationId": "export", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnExportReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExportFileRes"}}}}}}}, "/gov/txn/delete/1.0": {"post": {"tags": ["governmentService"], "summary": "Delete transaction", "description": "Delete transaction", "operationId": "delete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnDeleteReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/gov/txn/confirm/1.0": {"post": {"tags": ["governmentService"], "summary": "Confirm push transaction", "description": "Confirm push transaction", "operationId": "confirmPush", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnConfirmReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnProcessResultRes"}}}}}}}, "/gov/txn/approve/1.0": {"post": {"tags": ["governmentService"], "summary": "Init approval transaction", "description": "Init approval transaction", "operationId": "initApproval", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnApproveReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnInitApproveRes"}}}}}}}, "/gov/txn/approval/confirm/1.0": {"post": {"tags": ["governmentService"], "summary": "Confirm approval transaction", "description": "Confirm approval transaction", "operationId": "confirmApproval", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnConfirmReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnApprovalResultRes"}}}}}}}, "/gov/report/txn/print/1.0": {"post": {"tags": ["governmentService"], "summary": "Print document", "description": "Print document", "operationId": "print_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnPrintDocumentReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExportFileRes"}}}}}}}, "/gov/report/txn/list/1.0": {"post": {"tags": ["governmentService"], "summary": "List report transaction", "description": "List report transaction", "operationId": "listReport", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnReportListReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataListTxnReportListRes"}}}}}}}, "/gov/report/txn/export/1.0": {"post": {"tags": ["governmentService"], "summary": "Export report transaction", "description": "Export report transaction", "operationId": "export_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnExportReportReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExportFileRes"}}}}}}}, "/gov/report/txn/detail/1.0": {"post": {"tags": ["governmentService"], "summary": "Detail report transaction", "description": "Detail report transaction", "operationId": "detailReport", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnDetailRes"}}}}}}}, "/gov/par/treasury/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Treasury list", "description": "Treasury list", "operationId": "listTreasury", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTreasuryRes"}}}}}}}, "/gov/par/treasury/detail/1.0": {"post": {"tags": ["governmentService"], "summary": "Treasury detail", "description": "Treasury detail", "operationId": "detailTreasury", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdministrativeAreaReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TreasuryDetailRes"}}}}}}}, "/gov/par/tax-type/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Tax type list", "description": "Tax type list", "operationId": "listTaxType", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTaxTypeRes"}}}}}}}, "/gov/par/revenue-authority/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Revenue authority list", "description": "Revenue authority list", "operationId": "listRevenueAuthority", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevenueAuthorityReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListRevenueAuthorityRes"}}}}}}}, "/gov/par/revenue-account/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Revenue account list", "description": "Revenue account list", "operationId": "listRevenueAccount", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListRevenueAccountRes"}}}}}}}, "/gov/par/export-import-type/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Export import type list", "description": "Export import type list", "operationId": "listExportImportType", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListExportImportType"}}}}}}}, "/gov/par/economic-content/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Economic content list", "description": "Economic content list", "operationId": "listEconomicContent", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListEconomicContentRes"}}}}}}}, "/gov/par/customs-currency/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Customs currency list", "description": "Customs currency list", "operationId": "listCustomsCurrency", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListCustomsCurrencyRes"}}}}}}}, "/gov/par/chapter/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Chapter list", "description": "Chapter list", "operationId": "listChapter", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListChapterRes"}}}}}}}, "/gov/par/administrative-area/list/1.0": {"post": {"tags": ["governmentService"], "summary": "Administrative area list", "description": "Administrative area list", "operationId": "listAdministrativeArea", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdministrativeAreaReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListAdministrativeAreaRes"}}}}}}}, "/gov/customs-duty/txn/status/update/1.0": {"post": {"tags": ["governmentService"], "summary": "Update status transaction", "description": "Update status transaction", "operationId": "updateStatus", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnDetailRes"}}}}}}}, "/gov/customs-duty/txn/save/1.0": {"post": {"tags": ["governmentService"], "summary": "Save transaction", "description": "Save transaction", "operationId": "save", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnSaveReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/gov/customs-duty/txn/pending/list/1.0": {"post": {"tags": ["governmentService"], "summary": "List pending transaction", "description": "List pending transaction", "operationId": "listPending", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnPendingListReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataListTxnPendingListRes"}}}}}}}, "/gov/customs-duty/txn/list/1.0": {"post": {"tags": ["governmentService"], "summary": "List all transaction", "description": "List all transaction", "operationId": "list", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnListReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataListTxnListRes"}}}}}}}, "/gov/customs-duty/txn/edit/1.0": {"post": {"tags": ["governmentService"], "summary": "Edit transaction", "description": "Edit transaction", "operationId": "edit", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnSaveReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/gov/customs-duty/txn/detail/1.0": {"post": {"tags": ["governmentService"], "summary": "Detail transaction", "description": "Detail transaction", "operationId": "detail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TxnDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnDetailRes"}}}}}}}, "/gov/customs-duty/template/save/1.0": {"post": {"tags": ["governmentService"], "summary": "Save template", "description": "Save template", "operationId": "save_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateSaveReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/gov/customs-duty/template/list/1.0": {"post": {"tags": ["governmentService"], "summary": "List template", "description": "List template", "operationId": "list_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateListReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataListTemplateListRes"}}}}}}}, "/gov/customs-duty/tax/validate/1.0": {"post": {"tags": ["governmentService"], "summary": "Validate customs duty", "description": "Validate customs duty", "operationId": "validate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateCustomsDutyReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidateCustomsDutyRes"}}}}}}}, "/gov/customs-duty/tax/inquiry/1.0": {"post": {"tags": ["governmentService"], "summary": "Inquiry customs duty", "description": "Inquiry customs duty", "operationId": "inquiry", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InquiryCustomsDutyReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataListInquiryCustomsDutyRes"}}}}}}}, "/gov/customs-duty/batch/upload/1.0": {"post": {"tags": ["governmentService"], "summary": "Upload file batch", "description": "Upload file batch", "operationId": "uploadFile", "requestBody": {"content": {"application/json": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/gov/customs-duty/batch/tax/upload/1.0": {"post": {"tags": ["governmentService"], "summary": "Upload file", "description": "Upload file", "operationId": "uploadFile_1", "requestBody": {"content": {"application/json": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/gov/customs-duty/batch/tax/result/download/1.0": {"post": {"tags": ["governmentService"], "summary": "Download result", "description": "Download result", "operationId": "downloadResult", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExportFileRes"}}}}}}}, "/gov/customs-duty/batch/tax/list/1.0": {"post": {"tags": ["governmentService"], "summary": "List batch", "description": "List batch", "operationId": "list_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchListReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataListBatchListRes"}}}}}}}, "/gov/customs-duty/batch/tax/inquiry-result/export/1.0": {"post": {"tags": ["governmentService"], "summary": "Export inquiry result", "description": "Export inquiry result", "operationId": "exportInquiryResult", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExportFileRes"}}}}}}}, "/gov/customs-duty/batch/tax/download-template/1.0": {"post": {"tags": ["governmentService"], "summary": "Download template", "description": "Download template", "operationId": "downloadTemplate", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExportFileRes"}}}}}}}, "/gov/customs-duty/batch/result/download/1.0": {"post": {"tags": ["governmentService"], "summary": "Download result", "description": "Download result", "operationId": "downloadResult_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExportFileRes"}}}}}}}, "/gov/customs-duty/batch/push/1.0": {"post": {"tags": ["governmentService"], "summary": "Init push batch", "description": "Init push batch", "operationId": "initPush_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TxnInitPushRes"}}}}}}}, "/gov/customs-duty/batch/list/1.0": {"post": {"tags": ["governmentService"], "summary": "List batch", "description": "List batch", "operationId": "list_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchListReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DataListBatchListRes"}}}}}}}, "/gov/customs-duty/batch/file/download/1.0": {"post": {"tags": ["governmentService"], "summary": "Download file original", "description": "Download file original", "operationId": "downloadFile", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string", "description": "Data", "format": "byte"}}}}}}}}, "/gov/customs-duty/batch/fee/calc/1.0": {"post": {"tags": ["governmentService"], "summary": "Fee data", "description": "Fee data", "operationId": "calcFee", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BatchCalcFeeRes"}}}}}}}, "/gov/customs-duty/batch/download-template/1.0": {"post": {"tags": ["governmentService"], "summary": "Download batch template", "description": "Download batch template", "operationId": "downloadTemplate_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExportFileRes"}}}}}}}, "/gov/customs-duty/batch/detail/validate/1.0": {"post": {"tags": ["governmentService"], "summary": "Validate batch detail", "description": "Validate batch detail", "operationId": "validateDetail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateCustomsDutyReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ValidateCustomsDutyRes"}}}}}}}, "/gov/customs-duty/batch/detail/edit/1.0": {"post": {"tags": ["governmentService"], "summary": "Edit batch detail", "description": "Edit batch detail", "operationId": "editDetail", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDetailEditReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/gov/customs-duty/batch/detail/1.0": {"post": {"tags": ["governmentService"], "summary": "Check detail", "description": "Check detail", "operationId": "detail_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BatchDetailRes"}}}}}}}, "/gov/customs-duty/batch/delete/1.0": {"post": {"tags": ["governmentService"], "summary": "Delete batch", "description": "Delete batch", "operationId": "delete_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDetailReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/gov/customs-duty/batch/confirm/1.0": {"post": {"tags": ["governmentService"], "summary": "Confirm push batch", "description": "Confirm push batch", "operationId": "confirmPush_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchConfirmReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BatchProcessResultRes"}}}}}}}}, "components": {"schemas": {"TxnRejectReq": {"required": ["approvalNote", "txnIds"], "type": "object", "properties": {"txnIds": {"type": "array", "description": "Mã giao d<PERSON>ch ", "example": "['GOV231952', 'GOV931164']", "items": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Mã giao d<PERSON>ch ", "example": "['GOV231952', 'GOV931164']"}}, "approvalNote": {"maxLength": 100, "minLength": 0, "pattern": "^[A-Za-z0-9-_ ]*$", "type": "string", "description": "<PERSON>ý do từ chối", "example": "<PERSON> thong tin giao dich"}}}, "ResponseError": {"type": "object", "properties": {"errorCode": {"type": "string"}, "errorDesc": {"type": "string"}, "refVal": {"type": "object"}}, "description": "Addition Error List"}, "ResultTxnRejectRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnRejectRes"}}}, "TransactionResDetail": {"required": ["message", "txnId"], "type": "object", "properties": {"txnId": {"type": "string"}, "code": {"type": "string"}, "message": {"type": "string"}}, "description": "<PERSON><PERSON> s<PERSON>ch giao dịch từ chối thất bại"}, "TxnRejectRes": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "totalSuccess": {"type": "integer", "format": "int64"}, "failTxns": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch giao dịch từ chối thất bại", "items": {"$ref": "#/components/schemas/TransactionResDetail"}}, "txnId": {"type": "string", "description": "Mã giao d<PERSON>ch", "example": "GOV0125052700000086"}, "totalAmount": {"type": "string", "description": "<PERSON><PERSON><PERSON> số tiền", "example": *********}, "ccy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "totalAmountText": {"type": "string", "description": "<PERSON><PERSON> tiền bằng chữ", "example": "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi"}, "totalFail": {"type": "integer", "format": "int64"}}, "description": "Data"}, "TxnInitPushReq": {"required": ["type"], "type": "object", "properties": {"transKey": {"maxLength": 150, "minLength": 0, "type": "string", "description": "Key giao d<PERSON>ch", "example": "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86"}, "txnIds": {"type": "array", "description": "Mã giao d<PERSON>ch", "example": "['*******************', '*******************']", "items": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Mã giao d<PERSON>ch", "example": "['*******************', '*******************']"}}, "type": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "PUSH", "enum": ["PUSH_SAVE", "PUSH_EDIT", "PUSH"]}}}, "CustomQrCodeDataDto": {"type": "object", "properties": {"authId": {"type": "string"}, "userId": {"type": "string"}, "qrType": {"type": "string", "enum": ["BIDVIBANK_TRANS_OTP", "NAPAS"]}, "additionalInfo": {"type": "string"}, "productName": {"type": "string"}, "subProductName": {"type": "string"}, "productCode": {"type": "string"}, "subProductCode": {"type": "string"}, "amount": {"type": "string"}, "trans": {"type": "array", "items": {"$ref": "#/components/schemas/QrTransGroupDto"}}, "createdBy": {"type": "string"}, "totalTrans": {"type": "integer", "format": "int64"}}}, "QrTransGroupDto": {"type": "object", "properties": {"ccy": {"type": "string", "description": "CCY", "example": "VND"}, "amount": {"type": "string", "description": "amount", "example": "10000"}, "amountText": {"type": "string", "description": "amountText", "example": "Mot nghin dong"}, "total": {"type": "string", "description": "total", "example": "10"}}}, "ResultTxnInitPushRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnInitPushRes"}}}, "TransAuth": {"type": "object", "properties": {"authType": {"type": "string"}, "expTime": {"type": "integer", "format": "int64"}, "isSameDevice": {"type": "string"}, "qrData": {"type": "string"}, "authId": {"type": "string"}, "dataSign": {"type": "string"}, "additionalInfo": {"$ref": "#/components/schemas/CustomQrCodeDataDto"}, "serialNumber": {"type": "string"}, "provider": {"type": "string"}}}, "TxnInitPushRes": {"type": "object", "properties": {"transKey": {"type": "string"}, "requireAuth": {"type": "boolean"}, "transAuth": {"$ref": "#/components/schemas/TransAuth"}}, "description": "Data"}, "TxnPrintDocumentReq": {"required": ["txnIds"], "type": "object", "properties": {"txnIds": {"type": "array", "description": "Mã giao d<PERSON>ch", "example": "['*******************', '*******************']", "items": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Mã giao d<PERSON>ch", "example": "['*******************', '*******************']"}}}}, "ExportFileRes": {"type": "object", "properties": {"url": {"type": "string"}}, "description": "Data"}, "ResultExportFileRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ExportFileRes"}}}, "Filter": {"type": "object", "properties": {"operator": {"type": "string", "description": "ct/ eq/ neq/ gt/ gte/ lt/ lte"}, "field": {"type": "string"}, "value": {"type": "string"}}}, "Order": {"type": "object", "properties": {"field": {"type": "string"}, "direction": {"type": "string"}}}, "Page": {"type": "object", "properties": {"pageSize": {"minimum": 1, "type": "integer", "description": "Row number/ page, min = 1", "format": "int32"}, "pageNum": {"minimum": 1, "type": "integer", "description": "Page index (start from 1), min = 1", "format": "int32"}, "getTotal": {"type": "boolean", "description": "Get total record size flag"}}}, "TxnPendingApprovalListReq": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}, "startDate": {"type": "string", "description": "<PERSON><PERSON> ngày", "example": "2024-11-26"}, "endDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "2024-11-30"}, "minAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối thiểu", "example": 100000}, "maxAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối đa", "example": 1000000}, "ccys": {"maxItems": 3, "minItems": 0, "type": "array", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": ["VND", "USD"], "items": {"type": "string", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": "[\"VND\",\"USD\"]"}}, "debitAccNo": {"maxLength": 14, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "**********"}, "taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế", "example": "9581856"}, "declarationNo": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> tờ khai hải quan", "example": "8481929515"}, "batchNo": {"maxLength": 20, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u lô", "example": "1234567890"}, "txnTypes": {"type": "array", "description": "Loại giao dịch: 1 là thuế nội địa. 3 là phí hạ tầng cảng biển. 4 là thuế hải quan", "example": 4, "items": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Loại giao dịch: 1 là thuế nội địa. 3 là phí hạ tầng cảng biển. 4 là thuế hải quan", "example": "4"}}, "txnItemId": {"maxLength": 70, "minLength": 0, "type": "string", "description": "ID khoản nộp", "example": "9581856"}, "channels": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch kênh giao dịch", "items": {"maxLength": 70, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> s<PERSON>ch kênh giao dịch"}}}}, "DataListTxnPendingApprovalListRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnPendingApprovalListRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnPendingApprovalListRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnPendingApprovalListRes"}}}, "TxnPendingApprovalListRes": {"type": "object", "properties": {"txnId": {"type": "string"}, "debitAccNo": {"type": "string"}, "taxCode": {"type": "string"}, "declarationNo": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "batchNo": {"type": "string"}, "status": {"type": "string"}, "createdDate": {"type": "string"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "priority": {"type": "boolean"}, "orgId": {"type": "string"}, "raNote": {"type": "string"}, "approvalUsers": {"type": "string"}, "txnType": {"type": "string"}, "txnItemId": {"type": "string"}, "txnTypeName": {"type": "string"}, "statusName": {"type": "string"}}, "description": "List data"}, "TxnExportReq": {"type": "object", "properties": {"search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}, "startDate": {"type": "string", "description": "<PERSON><PERSON> ngày", "example": "2024-11-26"}, "endDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "2024-11-30"}, "minAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối thiểu", "example": 100000}, "maxAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối đa", "example": 1000000}, "ccys": {"maxItems": 3, "minItems": 0, "type": "array", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": ["VND", "USD"], "items": {"type": "string", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": "[\"VND\",\"USD\"]"}}, "statuses": {"type": "array", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": ["INIT"], "items": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "[\"INIT\"]"}}, "debitAccNo": {"maxLength": 14, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "**********"}, "taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế", "example": "9581856"}, "declarationNo": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> tờ khai hải quan", "example": "8481929515"}, "batchNo": {"maxLength": 20, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u lô", "example": "1234567890"}, "tccRefNo": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tham chiếu thuế"}, "txnTypes": {"type": "array", "description": "Loại giao dịch: 1 là thuế nội địa. 3 là phí hạ tầng cảng biển. 4 là thuế hải quan", "example": 4, "items": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Loại giao dịch: 1 là thuế nội địa. 3 là phí hạ tầng cảng biển. 4 là thuế hải quan", "example": "4"}}, "txnItemId": {"maxLength": 30, "minLength": 0, "type": "string", "description": "Id giao d<PERSON>ch"}, "channels": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch kênh giao dịch", "example": ["WEB", "APP"], "items": {"maxLength": 70, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> s<PERSON>ch kênh giao dịch", "example": "[\"WEB\",\"APP\"]"}}}}, "TxnDeleteReq": {"required": ["txnIds"], "type": "object", "properties": {"txnIds": {"type": "array", "description": "<PERSON><PERSON>c mã giao d<PERSON>ch", "example": ["12345", "67890"], "items": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON><PERSON>c mã giao d<PERSON>ch", "example": "[\"12345\",\"67890\"]"}}}}, "ResultString": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"type": "string", "description": "Data"}}}, "TxnConfirmReq": {"required": ["confirmValue", "transKey"], "type": "object", "properties": {"transKey": {"maxLength": 150, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> trị trả về từ API đẩy du<PERSON> (phục vụ truy vấn dữ liệu từ cache)", "example": "transKey"}, "confirmValue": {"type": "string", "description": "<PERSON><PERSON><PERSON> trị xác thực", "example": "confirmValue"}}}, "ResultTxnProcessResultRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnProcessResultRes"}}}, "TxnProcessResultRes": {"type": "object", "properties": {"total": {"type": "integer", "description": "Tổng số giao dịch đ<PERSON>", "format": "int64", "example": 10000}, "totalSuccess": {"type": "integer", "description": "Tổng số giao dịch đ<PERSON>y du<PERSON>t thành công", "format": "int64", "example": 10000}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "failTxns": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch giao dịch đ<PERSON> du<PERSON> thất bại", "example": "GOV0125052700000086", "items": {"$ref": "#/components/schemas/TransactionResDetail"}}, "txnId": {"type": "string", "description": "Mã giao d<PERSON>ch", "example": "GOV0125052700000086"}, "totalAmount": {"type": "string", "description": "<PERSON><PERSON><PERSON> số tiền", "example": *********}, "ccy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "feeTotal": {"type": "string", "description": "<PERSON><PERSON> (bao gồm VAT)", "example": 10000}, "feeCcy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của phí", "example": "VND"}, "feeOpt": {"type": "string", "description": "<PERSON><PERSON><PERSON> thức thu phí", "example": "<PERSON><PERSON>"}, "createdDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "debitAccNo": {"type": "string", "description": "Số tài k<PERSON>n trích nợ", "example": "1234567890"}, "debitAccName": {"type": "string", "description": "<PERSON>ên tài k<PERSON>n trích nợ", "example": "1234567890"}, "totalFail": {"type": "integer", "description": "Tổng số giao dịch đ<PERSON>y du<PERSON>t thất bại", "format": "int64", "example": 10000}, "totalAmountText": {"type": "string", "description": "<PERSON><PERSON> tiền bằng chữ", "example": "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi"}}, "description": "Data"}, "TxnApproveReq": {"required": ["txnIds"], "type": "object", "properties": {"txnIds": {"type": "array", "description": "<PERSON><PERSON>c mã giao d<PERSON>ch", "example": ["12345", "67890"], "items": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON><PERSON>c mã giao d<PERSON>ch", "example": "[\"12345\",\"67890\"]"}}}}, "ResultTxnInitApproveRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnInitApproveRes"}}}, "TxnInitApproveRes": {"type": "object", "properties": {"transKey": {"type": "string"}, "requireAuth": {"type": "boolean"}, "transAuth": {"$ref": "#/components/schemas/TransAuth"}}, "description": "Data"}, "ResultTxnApprovalResultRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnApprovalResultRes"}}}, "TxnApprovalResultRes": {"type": "object", "properties": {"total": {"type": "integer", "description": "Tổng số giao dịch đ<PERSON>", "format": "int64", "example": 10000}, "totalSuccess": {"type": "integer", "description": "Tổng số giao dịch đ<PERSON>y du<PERSON>t thành công", "format": "int64", "example": 10000}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "failTxns": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch giao dịch đ<PERSON> du<PERSON> thất bại", "example": "GOV0125052700000086", "items": {"$ref": "#/components/schemas/TransactionResDetail"}}, "txnId": {"type": "string", "description": "Mã giao d<PERSON>ch", "example": "GOV0125052700000086"}, "totalAmount": {"type": "string", "description": "<PERSON><PERSON><PERSON> số tiền", "example": *********}, "ccy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "feeTotal": {"type": "string", "description": "<PERSON><PERSON> (bao gồm VAT)", "example": 10000}, "feeCcy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của phí", "example": "VND"}, "feeOpt": {"type": "string", "description": "<PERSON><PERSON><PERSON> thức thu phí", "example": "<PERSON><PERSON>"}, "createdDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "debitAccNo": {"type": "string", "description": "Số tài k<PERSON>n trích nợ", "example": "1234567890"}, "debitAccName": {"type": "string", "description": "<PERSON>ên tài k<PERSON>n trích nợ", "example": "1234567890"}, "approvedDate": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON>h<PERSON>"}, "isFinal": {"type": "boolean", "description": "<PERSON><PERSON> phải là giao dịch cuối cùng không", "example": true}, "status": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái", "example": "PROCESSING"}, "statusName": {"type": "string", "description": "<PERSON>ên trạng thái", "example": "<PERSON><PERSON> lý"}, "totalFail": {"type": "integer", "description": "Tổng số giao dịch đ<PERSON>y du<PERSON>t thất bại", "format": "int64", "example": 10000}, "totalAmountText": {"type": "string", "description": "<PERSON><PERSON> tiền bằng chữ", "example": "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi"}}, "description": "Data"}, "TxnReportListReq": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}, "startDate": {"type": "string", "description": "<PERSON><PERSON> ngày", "example": "2024-11-26"}, "endDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "2024-11-30"}, "minAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối thiểu", "example": 100000}, "maxAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối đa", "example": 1000000}, "ccys": {"maxItems": 3, "minItems": 0, "type": "array", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": ["VND", "USD"], "items": {"type": "string", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": "[\"VND\",\"USD\"]"}}, "debitAccNo": {"maxLength": 14, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "**********"}, "taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế", "example": "9581856"}, "declarationNo": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> tờ khai hải quan", "example": "8481929515"}, "batchNo": {"maxLength": 20, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u lô", "example": "1234567890"}, "txnTypes": {"type": "array", "description": "Loại giao dịch: 1 là thuế nội địa. 3 là phí hạ tầng cảng biển. 4 là thuế hải quan", "example": 4, "items": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Loại giao dịch: 1 là thuế nội địa. 3 là phí hạ tầng cảng biển. 4 là thuế hải quan", "example": "4"}}, "txnItemId": {"maxLength": 70, "minLength": 0, "type": "string", "description": "ID khoản nộp", "example": "9581856"}, "channels": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch kênh giao dịch", "items": {"maxLength": 70, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> s<PERSON>ch kênh giao dịch"}}, "statuses": {"type": "array", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": ["INIT"], "items": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "[\"INIT\"]"}}, "coreRef": {"maxLength": 20, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON> tham chiếu thuế", "example": "190274235"}}}, "DataListTxnReportListRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnReportListRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnReportListRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnReportListRes"}}}, "TxnReportListRes": {"type": "object", "properties": {"txnId": {"type": "string"}, "debitAccNo": {"type": "string"}, "taxCode": {"type": "string"}, "declarationNo": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "batchNo": {"type": "string"}, "status": {"type": "string"}, "createdDate": {"type": "string"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "priority": {"type": "boolean"}, "orgId": {"type": "string"}, "createdBy": {"type": "string"}, "approvalUsers": {"type": "string"}, "txnType": {"type": "string"}, "txnItemId": {"type": "string"}, "txnTypeName": {"type": "string"}, "statusName": {"type": "string"}}, "description": "List data"}, "TxnExportReportReq": {"type": "object", "properties": {"search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}, "startDate": {"type": "string", "description": "<PERSON><PERSON> ngày", "example": "2024-11-26"}, "endDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "2024-11-30"}, "minAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối thiểu", "example": 100000}, "maxAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối đa", "example": 1000000}, "ccys": {"maxItems": 3, "minItems": 0, "type": "array", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": ["VND", "USD"], "items": {"type": "string", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": "[\"VND\",\"USD\"]"}}, "statuses": {"type": "array", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": ["INIT"], "items": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "[\"INIT\"]"}}, "debitAccNo": {"maxLength": 14, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "**********"}, "taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế", "example": "9581856"}, "declarationNo": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> tờ khai hải quan", "example": "8481929515"}, "batchNo": {"maxLength": 20, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u lô", "example": "1234567890"}, "tccRefNo": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> tham chiếu thuế"}, "txnTypes": {"type": "array", "description": "Loại giao dịch: 1 là thuế nội địa. 3 là phí hạ tầng cảng biển. 4 là thuế hải quan", "example": 4, "items": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Loại giao dịch: 1 là thuế nội địa. 3 là phí hạ tầng cảng biển. 4 là thuế hải quan", "example": "4"}}, "txnItemId": {"maxLength": 30, "minLength": 0, "type": "string", "description": "Id giao d<PERSON>ch"}, "channels": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch kênh giao dịch", "example": ["WEB", "APP"], "items": {"maxLength": 70, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> s<PERSON>ch kênh giao dịch", "example": "[\"WEB\",\"APP\"]"}}}}, "TxnDetailReq": {"required": ["txnId"], "type": "object", "properties": {"txnId": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Mã giao d<PERSON>ch", "example": "DVC01704202411252339"}, "checkPerm": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> tra ng<PERSON><PERSON><PERSON> du<PERSON> tiếp theo", "example": true}}}, "ResultTxnDetailRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TxnDetailRes"}}}, "TxnDetailRes": {"type": "object", "properties": {"accountingStatus": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái hạch toán", "example": "0"}, "accountingStatusName": {"type": "string", "description": "<PERSON><PERSON>n trạng thái hạch toán", "example": "<PERSON><PERSON><PERSON><PERSON> công"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "altPayerAddr": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "Đ<PERSON>a chỉ người nộp thay", "example": "<PERSON><PERSON>"}, "altPayerName": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> thay", "example": "<PERSON><PERSON><PERSON>"}, "altTaxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế người nộp thay", "example": "**********"}, "amount": {"type": "string", "description": "<PERSON><PERSON> tiền", "example": *********}, "amountText": {"type": "string", "description": "<PERSON><PERSON> tiền bằng chữ", "example": "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi"}, "approvalNote": {"type": "string", "description": "<PERSON>ý do từ chối", "example": "<PERSON>ý do từ chối"}, "approvedBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>", "example": "88060ktv"}, "batchNo": {"type": "string", "description": "Mã lô", "example": "BATCH001"}, "benBankCode": {"type": "string", "description": "<PERSON><PERSON> hàng thụ hưởng", "example": "********"}, "benBankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ngân hàng thụ hưởng", "example": "<PERSON><PERSON> hàng thụ hưởng ********"}, "ccy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "createdBy": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> tạo", "example": "88060ktv"}, "createdDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "customsConnStatus": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái kết nối hải quan", "example": "0"}, "customsConnStatusName": {"type": "string", "description": "<PERSON>ên trạng thái kết nối hải quan", "example": "<PERSON><PERSON><PERSON><PERSON> công"}, "debitAccName": {"type": "string", "description": " <PERSON>ên tài k<PERSON>n trích nợ", "example": "Công ty TNHH A"}, "debitAccNo": {"type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "**********"}, "description": {"type": "string", "description": "<PERSON><PERSON>", "example": "<PERSON><PERSON>"}, "feeCcy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ phí", "example": "VND"}, "feeOpt": {"type": "string", "description": "<PERSON><PERSON><PERSON> thức thu phí", "example": "<PERSON><PERSON>"}, "feeTotal": {"type": "string", "description": "<PERSON><PERSON> (bao gồm VAT)", "example": 10000}, "orgId": {"type": "string", "description": "Mã giao dịch kh<PERSON>ch hàng", "example": "235947523904"}, "payerAddr": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> chỉ người nộp thuế", "example": "<PERSON><PERSON>"}, "payerName": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON> thuế", "example": "<PERSON><PERSON><PERSON>"}, "payerType": {"type": "integer", "description": "<PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON><PERSON><PERSON> nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2 - c<PERSON> nhân", "format": "int32", "example": 1}, "payerTypeName": {"type": "string"}, "priority": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> tiên", "example": true}, "raNote": {"type": "string", "description": "<PERSON><PERSON> chú tới ng<PERSON><PERSON>", "example": "<PERSON><PERSON> chú tới ng<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "status": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "SUCCESS"}, "statusName": {"type": "string", "description": "<PERSON>ên trạng thái", "example": "<PERSON><PERSON><PERSON><PERSON> công"}, "taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế người nộp", "example": "**********"}, "taxItems": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch thông tin k<PERSON>n nộp", "items": {"$ref": "#/components/schemas/TxnTaxFullItemDto"}}, "tccRefNo": {"type": "string", "description": "Số ref TCC", "example": "88060ktv"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "txnId": {"type": "string", "description": "Mã giao d<PERSON>ch", "example": "TXN001"}}, "description": "Data"}, "TxnTaxFullItemDto": {"required": ["amount", "ccCode", "ccy", "chapterCode", "declarationDate", "declarationNo", "ecCode", "eiTypeCode", "taxTypeCode", "transDesc"], "type": "object", "properties": {"eiTypeCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại hình xuất nhập khẩu", "example": "A11"}, "taxTypeCode": {"maxLength": 10, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> sắc thuế", "example": "VA"}, "ccCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại tiền hải quan", "example": "1"}, "chapterCode": {"maxLength": 3, "minLength": 0, "type": "string", "description": "Mã ch<PERSON>", "example": "755"}, "ecCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> nội dung kinh tế", "example": "3063"}, "amount": {"type": "string", "description": "<PERSON><PERSON> tiền", "example": "100000"}, "ccy": {"maxLength": 3, "minLength": 0, "pattern": "^[A-Za-z]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của số tiền nhập", "example": "VND"}, "declarationDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> tờ khai", "example": "2021-01-01"}, "declarationNo": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> tờ khai", "example": "1234567890"}, "transDesc": {"maxLength": 210, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch", "example": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch"}, "ccName": {"type": "string", "description": "<PERSON><PERSON>n lo<PERSON>i tiền hải quan", "example": "<PERSON><PERSON><PERSON> xu<PERSON>t nh<PERSON> kh<PERSON>u"}, "chapterName": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "<PERSON><PERSON> tế tư nhân"}, "ecName": {"type": "string", "description": "<PERSON><PERSON><PERSON> n<PERSON>i dung kinh tế", "example": "<PERSON><PERSON> phí cấp gi<PERSON>y phép quy ho<PERSON>ch"}, "eiTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i hình xuất nhập kh<PERSON>u", "example": "<PERSON><PERSON><PERSON><PERSON> kinh doanh tiêu dùng"}, "taxTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> thuế", "example": "<PERSON><PERSON><PERSON> giá trị gia tăng"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "benBankCode": {"type": "string", "description": "<PERSON><PERSON> hàng thụ hưởng", "example": "********"}, "benBankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ngân hàng thụ hưởng", "example": "<PERSON><PERSON> hàng thụ hưởng ********"}, "payerType": {"type": "integer", "description": "<PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON><PERSON><PERSON> nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2 - c<PERSON> nhân", "format": "int32", "example": 1}, "payerTypeName": {"type": "string"}}, "description": "<PERSON><PERSON> s<PERSON>ch thông tin k<PERSON>n nộp"}, "DataListTreasuryRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TreasuryRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTreasuryRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTreasuryRes"}}}, "TreasuryRes": {"type": "object", "properties": {"treasuryCode": {"type": "string"}, "treasuryName": {"type": "string"}, "admAreaCode": {"type": "string"}, "admAreaName": {"type": "string"}}, "description": "List data"}, "AdministrativeAreaReq": {"type": "object", "properties": {"treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "1234567"}}}, "ResultTreasuryDetailRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TreasuryDetailRes"}}}, "TreasuryDetailRes": {"type": "object", "properties": {"treasuryCode": {"type": "string"}, "benBankCode": {"type": "string"}, "benBankName": {"type": "string"}, "isInBidv": {"type": "boolean"}}, "description": "Data"}, "DataListTaxTypeRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TaxTypeRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTaxTypeRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTaxTypeRes"}}}, "TaxTypeRes": {"type": "object", "properties": {"taxTypeCode": {"type": "string"}, "taxTypeName": {"type": "string"}}, "description": "List data"}, "RevenueAuthorityReq": {"required": ["treasuryCode"], "type": "object", "properties": {"treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "1234567"}}}, "DataListRevenueAuthorityRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/RevenueAuthorityRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListRevenueAuthorityRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListRevenueAuthorityRes"}}}, "RevenueAuthorityRes": {"type": "object", "properties": {"revAuthCode": {"type": "string"}, "revAuthName": {"type": "string"}}, "description": "List data"}, "DataListRevenueAccountRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/RevenueAccountRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListRevenueAccountRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListRevenueAccountRes"}}}, "RevenueAccountRes": {"type": "object", "properties": {"revAccCode": {"type": "string"}, "revAccName": {"type": "string"}}, "description": "List data"}, "DataListExportImportType": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ExportImportType"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ExportImportType": {"type": "object", "properties": {"eiTypeCode": {"type": "string"}, "eiTypeName": {"type": "string"}}, "description": "List data"}, "ResultListExportImportType": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListExportImportType"}}}, "DataListEconomicContentRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/EconomicContentRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "EconomicContentRes": {"type": "object", "properties": {"ecCode": {"type": "string"}, "ecName": {"type": "string"}}, "description": "List data"}, "ResultListEconomicContentRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListEconomicContentRes"}}}, "CustomsCurrencyRes": {"type": "object", "properties": {"ccCode": {"type": "string"}, "ccName": {"type": "string"}}, "description": "List data"}, "DataListCustomsCurrencyRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/CustomsCurrencyRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListCustomsCurrencyRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListCustomsCurrencyRes"}}}, "ChapterRes": {"type": "object", "properties": {"chapterCode": {"type": "string"}, "chapterName": {"type": "string"}}, "description": "List data"}, "DataListChapterRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ChapterRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListChapterRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListChapterRes"}}}, "AdministrativeAreaRes": {"type": "object", "properties": {"admAreaCode": {"type": "string"}, "admAreaName": {"type": "string"}}, "description": "List data"}, "DataListAdministrativeAreaRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/AdministrativeAreaRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListAdministrativeAreaRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListAdministrativeAreaRes"}}}, "TxnSaveReq": {"required": ["transKey"], "type": "object", "properties": {"transKey": {"maxLength": 150, "minLength": 0, "type": "string", "description": "Key giao d<PERSON>ch", "example": "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86"}}}, "TxnPendingListReq": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}, "startDate": {"type": "string", "description": "<PERSON><PERSON> ngày", "example": "2024-11-26"}, "endDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "2024-11-30"}, "minAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối thiểu", "example": 100000}, "maxAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối đa", "example": 1000000}, "ccys": {"maxItems": 3, "minItems": 0, "type": "array", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": ["VND", "USD"], "items": {"type": "string", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": "[\"VND\",\"USD\"]"}}, "debitAccNo": {"maxLength": 14, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "**********"}, "taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế", "example": "9581856"}, "declarationNo": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> tờ khai hải quan", "example": "8481929515"}, "batchNo": {"maxLength": 20, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u lô", "example": "1234567890"}, "statuses": {"type": "array", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": ["INIT"], "items": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "[\"INIT\"]"}}}}, "DataListTxnPendingListRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnPendingListRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnPendingListRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnPendingListRes"}}}, "TxnPendingListRes": {"type": "object", "properties": {"txnId": {"type": "string"}, "debitAccNo": {"type": "string"}, "taxCode": {"type": "string"}, "declarationNo": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "batchNo": {"type": "string"}, "status": {"type": "string"}, "createdDate": {"type": "string"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "priority": {"type": "boolean"}, "orgId": {"type": "string"}, "statusName": {"type": "string"}}, "description": "List data"}, "TxnListReq": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}, "startDate": {"type": "string", "description": "<PERSON><PERSON> ngày", "example": "2024-11-26"}, "endDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "2024-11-30"}, "minAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối thiểu", "example": 100000}, "maxAmount": {"type": "string", "description": "<PERSON><PERSON> tiền tối đa", "example": 1000000}, "ccys": {"maxItems": 3, "minItems": 0, "type": "array", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": ["VND", "USD"], "items": {"type": "string", "description": "<PERSON><PERSON> s<PERSON>ch loại tiền tệ", "example": "[\"VND\",\"USD\"]"}}, "debitAccNo": {"maxLength": 14, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "**********"}, "taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế", "example": "9581856"}, "declarationNo": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> tờ khai hải quan", "example": "8481929515"}, "batchNo": {"maxLength": 20, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u lô", "example": "1234567890"}, "statuses": {"type": "array", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": ["INIT"], "items": {"type": "string", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "[\"INIT\"]"}}}}, "DataListTxnListRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TxnListRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTxnListRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTxnListRes"}}}, "TxnListRes": {"type": "object", "properties": {"txnId": {"type": "string"}, "debitAccNo": {"type": "string"}, "taxCode": {"type": "string"}, "declarationNo": {"type": "string"}, "amount": {"type": "string"}, "ccy": {"type": "string"}, "batchNo": {"type": "string"}, "status": {"type": "string"}, "createdDate": {"type": "string"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "priority": {"type": "boolean"}, "orgId": {"type": "string"}, "approvalUsers": {"type": "string"}, "createdBy": {"type": "string"}, "statusName": {"type": "string"}}, "description": "List data"}, "TemplateSaveReq": {"required": ["templateName", "txnId"], "type": "object", "properties": {"txnId": {"maxLength": 50, "minLength": 0, "type": "string", "description": "Mã giao d<PERSON>ch", "example": "DVC01704202411252339"}, "templateName": {"maxLength": 40, "minLength": 0, "pattern": "^[A-Za-z0-9.\\- ]*$", "type": "string", "description": "Tên template", "example": "Mẫu n<PERSON><PERSON> thuế 1"}, "isPublic": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> mẫu công khai", "example": true}}}, "TemplateListReq": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}}}, "DataListTemplateListRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TemplateListRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTemplateListRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTemplateListRes"}}}, "TemplateListRes": {"type": "object", "properties": {"taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế người nộp", "example": "**********"}, "altTaxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế người nộp thay", "example": "**********"}, "payerName": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON> thuế", "example": "<PERSON><PERSON><PERSON>"}, "altPayerName": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> thay", "example": "<PERSON><PERSON><PERSON>"}, "payerAddr": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> chỉ người nộp thuế", "example": "<PERSON><PERSON>"}, "altPayerAddr": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "Đ<PERSON>a chỉ người nộp thay", "example": "<PERSON><PERSON>"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "benBankCode": {"type": "string", "description": "<PERSON><PERSON> hàng thụ hưởng", "example": "********"}, "benBankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ngân hàng thụ hưởng", "example": "<PERSON><PERSON> hàng thụ hưởng ********"}, "payerType": {"type": "integer", "description": "<PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON><PERSON><PERSON> nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2 - c<PERSON> nhân", "format": "int32", "example": 1}, "debitAccNo": {"type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "**********"}, "templateId": {"type": "string", "description": "<PERSON><PERSON> mẫu giao d<PERSON>ch", "example": "26CE7FAC-29E1-4F33-9DE7-0830BFDB0122"}, "templateName": {"type": "string", "description": "<PERSON><PERSON>n mẫu giao dịch", "example": "Mẫu giao d<PERSON>ch 1"}, "createdDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "taxItems": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch thông tin k<PERSON>n nộp", "items": {"$ref": "#/components/schemas/TxnTaxFullItemDto"}}, "payerTypeName": {"type": "string"}}, "description": "List data"}, "TxnTaxItemDto": {"required": ["amount", "ccCode", "ccy", "chapterCode", "declarationDate", "declarationNo", "ecCode", "eiTypeCode", "taxTypeCode", "transDesc"], "type": "object", "properties": {"eiTypeCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại hình xuất nhập khẩu", "example": "A11"}, "taxTypeCode": {"maxLength": 10, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> sắc thuế", "example": "VA"}, "ccCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại tiền hải quan", "example": "1"}, "chapterCode": {"maxLength": 3, "minLength": 0, "type": "string", "description": "Mã ch<PERSON>", "example": "755"}, "ecCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> nội dung kinh tế", "example": "3063"}, "amount": {"type": "string", "description": "<PERSON><PERSON> tiền", "example": "100000"}, "ccy": {"maxLength": 3, "minLength": 0, "pattern": "^[A-Za-z]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của số tiền nhập", "example": "VND"}, "declarationDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> tờ khai", "example": "2021-01-01"}, "declarationNo": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> tờ khai", "example": "1234567890"}, "transDesc": {"maxLength": 210, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch", "example": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch"}}, "description": "<PERSON><PERSON> s<PERSON>ch thông tin k<PERSON>n nộp"}, "ValidateCustomsDutyReq": {"required": ["admAreaCode", "amount", "ccy", "debitAccNo", "payerAddr", "payerName", "payerType", "revAccCode", "revAuthCode", "taxCode", "taxItems", "treasuryCode"], "type": "object", "properties": {"amount": {"type": "string", "description": "<PERSON><PERSON> tiền", "example": "100000"}, "ccy": {"maxLength": 3, "minLength": 0, "pattern": "^[A-Za-z]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của số tiền nhập", "example": "VND"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế người nộp", "example": "**********"}, "payerName": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON> thuế", "example": "<PERSON><PERSON><PERSON>"}, "payerAddr": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> chỉ người nộp thuế", "example": "<PERSON><PERSON>"}, "altTaxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế người nộp thay", "example": "**********"}, "altPayerName": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> thay", "example": "<PERSON><PERSON><PERSON>"}, "altPayerAddr": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "Đ<PERSON>a chỉ người nộp thay", "example": "<PERSON><PERSON>"}, "debitAccNo": {"maxLength": 14, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "**********"}, "txnId": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> giao dịch (bắt buộc khi trong luồng chỉnh sửa giao dịch)", "example": "DVC01704202411252339"}, "payerType": {"type": "string", "description": "<PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON><PERSON><PERSON> nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2 - c<PERSON> nhân", "example": "1", "enum": ["UNDEFINED", "BUSINESS", "INDIVIDUAL"]}, "raNote": {"maxLength": 100, "minLength": 0, "pattern": "^[A-Za-z0-9-_ ]*$", "type": "string", "description": "<PERSON><PERSON> chú tới ng<PERSON><PERSON>", "example": "<PERSON><PERSON><PERSON> thu<PERSON> do<PERSON>h nghi<PERSON><PERSON> hàng tháng"}, "taxItems": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch thông tin k<PERSON>n nộp", "items": {"$ref": "#/components/schemas/TxnTaxItemDto"}}, "priority": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> tiên", "example": true}, "orgId": {"maxLength": 30, "minLength": 0, "pattern": "^[A-Za-z0-9-_ ]*$", "type": "string", "description": "Mã giao dịch kh<PERSON>ch hàng", "example": "235947523904"}}}, "ResultValidateCustomsDutyRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/ValidateCustomsDutyRes"}}}, "ValidateCustomsDutyRes": {"type": "object", "properties": {"transKey": {"type": "string", "description": "Key giao d<PERSON>ch", "example": "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86"}, "feeTotal": {"type": "string", "description": "<PERSON><PERSON> (bao gồm VAT)", "example": 10000}, "feeCcy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "feeOpt": {"type": "string", "description": "<PERSON><PERSON><PERSON> thức thu phí", "example": "<PERSON><PERSON>"}, "amount": {"type": "string", "description": "<PERSON><PERSON> tiền", "example": *********}, "ccy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ", "example": "VND"}, "amountText": {"type": "string", "description": "<PERSON><PERSON> tiền bằng chữ", "example": "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi"}}, "description": "Data"}, "InquiryCustomsDutyReq": {"required": ["taxCode"], "type": "object", "properties": {"taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế người nộp", "example": "**********"}, "declarationNo": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> tờ khai <PERSON> quan", "example": "43233242423"}, "declarationYear": {"maxLength": 4, "minLength": 0, "pattern": "^[0-9]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> tờ khai", "example": "2025"}}}, "DataListInquiryCustomsDutyRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/InquiryCustomsDutyRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "InquiryCustomsDutyRes": {"required": ["amount", "ccCode", "ccy", "chapterCode", "declarationDate", "declarationNo", "ecCode", "eiTypeCode", "taxTypeCode", "transDesc"], "type": "object", "properties": {"eiTypeCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại hình xuất nhập khẩu", "example": "A11"}, "taxTypeCode": {"maxLength": 10, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> sắc thuế", "example": "VA"}, "ccCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại tiền hải quan", "example": "1"}, "chapterCode": {"maxLength": 3, "minLength": 0, "type": "string", "description": "Mã ch<PERSON>", "example": "755"}, "ecCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> nội dung kinh tế", "example": "3063"}, "amount": {"type": "string", "description": "<PERSON><PERSON> tiền", "example": "100000"}, "ccy": {"maxLength": 3, "minLength": 0, "pattern": "^[A-Za-z]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của số tiền nhập", "example": "VND"}, "declarationDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> tờ khai", "example": "2021-01-01"}, "declarationNo": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> tờ khai", "example": "1234567890"}, "transDesc": {"maxLength": 210, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch", "example": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch"}, "ccName": {"type": "string", "description": "<PERSON><PERSON>n lo<PERSON>i tiền hải quan", "example": "<PERSON><PERSON><PERSON> xu<PERSON>t nh<PERSON> kh<PERSON>u"}, "chapterName": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "<PERSON><PERSON> tế tư nhân"}, "ecName": {"type": "string", "description": "<PERSON><PERSON><PERSON> n<PERSON>i dung kinh tế", "example": "<PERSON><PERSON> phí cấp gi<PERSON>y phép quy ho<PERSON>ch"}, "eiTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i hình xuất nhập kh<PERSON>u", "example": "<PERSON><PERSON><PERSON><PERSON> kinh doanh tiêu dùng"}, "taxTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> thuế", "example": "<PERSON><PERSON><PERSON> giá trị gia tăng"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}, "admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "benBankCode": {"type": "string", "description": "<PERSON><PERSON> hàng thụ hưởng", "example": "********"}, "benBankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ngân hàng thụ hưởng", "example": "<PERSON><PERSON> hàng thụ hưởng ********"}, "payerType": {"type": "integer", "description": "<PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON><PERSON><PERSON> nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2 - c<PERSON> nhân", "format": "int32", "example": 1}, "payerTypeName": {"type": "string"}}, "description": "Th<PERSON>ng tin tra cứu thuế hải quan"}, "ResultListInquiryCustomsDutyRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListInquiryCustomsDutyRes"}}}, "BatchDetailReq": {"required": ["batchNo"], "type": "object", "properties": {"batchNo": {"maxLength": 20, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "Mã lô", "example": "BDR20250529172533579"}}}, "BatchListReq": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "search": {"maxLength": 70, "minLength": 0, "type": "string", "description": "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ", "example": "Nop thue"}, "startDate": {"type": "string", "description": "<PERSON><PERSON> ngày", "example": "2024-11-26"}, "endDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "2024-11-30"}, "batchName": {"maxLength": 30, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s\\-_() ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> bảng kê", "example": "<PERSON><PERSON> <PERSON><PERSON> ngh<PERSON>a vụ thuế"}, "statuses": {"type": "array", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": ["INIT"], "items": {"maxLength": 70, "minLength": 0, "type": "string", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "example": "[\"INIT\"]"}}, "batchNo": {"maxLength": 20, "minLength": 0, "pattern": "^[A-Za-z0-9]*$", "type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u lô", "example": "1234567890"}}}, "BatchListRes": {"type": "object", "properties": {"batchId": {"type": "string", "description": "<PERSON>d l<PERSON>", "example": "F2B4067F550441739FE10B63B9235715"}, "createdDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "status": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> thái", "example": "PROCESSING"}, "batchName": {"type": "string", "description": "<PERSON><PERSON><PERSON> bảng kê", "example": "<PERSON><PERSON> <PERSON><PERSON> ngh<PERSON>a vụ thuế"}, "batchNo": {"type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u lô", "example": "1234567890"}, "processedDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> lý"}, "statusName": {"type": "string", "description": "<PERSON>ên trạng thái", "example": "<PERSON><PERSON> lý"}}, "description": "List data"}, "DataListBatchListRes": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/BatchListRes"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListBatchListRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListBatchListRes"}}}, "ResultByte[]": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"type": "array", "description": "Data", "items": {"type": "string", "description": "Data", "format": "byte"}}}}, "BatchCalcFeeRes": {"type": "object", "properties": {"batchNo": {"type": "string", "description": "Số lô", "example": "BDR20250607215402732"}, "batchType": {"type": "string", "description": "Loại lô: PAYMENT: <PERSON><PERSON><PERSON> thuế theo lô, INQUIRY: <PERSON><PERSON><PERSON> tin theo lô", "example": "PAYMENT"}, "ccy": {"type": "string", "description": "<PERSON><PERSON> tiền tệ", "example": "VND"}, "feeCcy": {"type": "string", "description": "<PERSON><PERSON> tiền tệ phí", "example": "VND"}, "feeOpt": {"type": "string", "description": "<PERSON><PERSON><PERSON> thức thu phí", "example": "<PERSON><PERSON>"}, "fileName": {"type": "string", "description": "Tên file", "example": "GOV_PAYMENT_BATCH_001"}, "fileSize": {"type": "integer", "description": "<PERSON><PERSON><PERSON> file", "format": "int64", "example": 1000000}, "items": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch item", "items": {"$ref": "#/components/schemas/BatchItemDetailDto"}}, "totalAmount": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền", "example": 1000000}, "totalAmountText": {"type": "string"}, "totalFee": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền phí dịch v<PERSON> (bao gồm VAT)", "example": 1000000}, "totalItems": {"type": "integer", "format": "int32"}}, "description": "Data"}, "BatchItemDetailDto": {"type": "object", "properties": {"admAreaCode": {"maxLength": 5, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> đ<PERSON>a bàn hành <PERSON>h", "example": "009HH"}, "admAreaName": {"type": "string", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> bàn hành ch<PERSON>h", "example": "<PERSON><PERSON>"}, "altPayerAddr": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "Đ<PERSON>a chỉ người nộp thay", "example": "<PERSON><PERSON>"}, "altPayerName": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> thay", "example": "<PERSON><PERSON><PERSON>"}, "altTaxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế người nộp thay", "example": "**********"}, "amount": {"type": "string", "description": "<PERSON><PERSON> tiền", "example": "100000"}, "amountText": {"type": "string", "description": "<PERSON><PERSON> tiền bằng chữ", "example": "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi"}, "batchItemId": {"type": "string", "description": "ID của item trong batch", "example": "a614d77b-9ee6-481f-b321-838ed39ddba8"}, "benBankCode": {"type": "string", "description": "<PERSON><PERSON> hàng thụ hưởng", "example": "********"}, "benBankName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ngân hàng thụ hưởng", "example": "<PERSON><PERSON> hàng thụ hưởng ********"}, "ccCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại tiền hải quan", "example": "1"}, "ccName": {"type": "string", "description": "<PERSON><PERSON>n lo<PERSON>i tiền hải quan", "example": "<PERSON><PERSON><PERSON> xu<PERSON>t nh<PERSON> kh<PERSON>u"}, "ccy": {"maxLength": 3, "minLength": 0, "pattern": "^[A-Za-z]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ của số tiền nhập", "example": "VND"}, "chapterCode": {"maxLength": 3, "minLength": 0, "type": "string", "description": "Mã ch<PERSON>", "example": "755"}, "chapterName": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "example": "<PERSON><PERSON> tế tư nhân"}, "createdDate": {"type": "string", "description": "<PERSON><PERSON><PERSON>"}, "debitAccNo": {"type": "string", "description": "<PERSON><PERSON><PERSON>n trích nợ", "example": "**********"}, "declarationDate": {"type": "string", "description": "<PERSON><PERSON><PERSON> tờ khai", "example": "24/05/2024"}, "declarationNo": {"type": "string", "description": "<PERSON><PERSON> tờ khai", "example": "30644771632"}, "declarationYear": {"type": "string", "description": "<PERSON><PERSON><PERSON> tờ khai", "example": "2024"}, "ecCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> nội dung kinh tế", "example": "3063"}, "ecName": {"type": "string", "description": "<PERSON><PERSON><PERSON> n<PERSON>i dung kinh tế", "example": "<PERSON><PERSON> phí cấp gi<PERSON>y phép quy ho<PERSON>ch"}, "eiTypeCode": {"maxLength": 20, "minLength": 0, "type": "string", "description": "<PERSON>ã loại hình xuất nhập khẩu", "example": "A11"}, "eiTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i hình xuất nhập kh<PERSON>u", "example": "<PERSON><PERSON><PERSON><PERSON> kinh doanh tiêu dùng"}, "errors": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch lỗi", "example": "GOV0001, GOV0002", "items": {"$ref": "#/components/schemas/ErrMsgDto"}}, "feeCcy": {"type": "string", "description": "<PERSON><PERSON><PERSON> tiền tệ phí", "example": "VND"}, "feeTotal": {"type": "string", "description": "<PERSON><PERSON> (bao gồm VAT)", "example": 10000}, "orgId": {"type": "string"}, "payerAddr": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON> chỉ người nộp thuế", "example": "<PERSON><PERSON>"}, "payerName": {"maxLength": 140, "minLength": 0, "pattern": "^[a-zA-Z0-9À-ỹ\\s@&().\\-+_,/ ]*$", "type": "string", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON> thuế", "example": "<PERSON><PERSON><PERSON>"}, "payerType": {"type": "string", "description": " <PERSON><PERSON><PERSON> h<PERSON>nh ng<PERSON><PERSON><PERSON> nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2 - c<PERSON> nhân", "example": "1"}, "payerTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON><PERSON> nộp thuế", "example": "<PERSON><PERSON><PERSON>"}, "revAccCode": {"maxLength": 50, "minLength": 0, "type": "string", "description": "<PERSON>ã tài k<PERSON>n thu", "example": "1898"}, "revAccName": {"type": "string", "description": "<PERSON><PERSON><PERSON> tài k<PERSON>n thu", "example": "<PERSON><PERSON><PERSON> thu 1898"}, "revAuthCode": {"maxLength": 7, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> cơ quan thu", "example": "1420"}, "revAuthName": {"type": "string", "description": "<PERSON><PERSON><PERSON> c<PERSON> quan thu", "example": "<PERSON><PERSON> quan thu 1420"}, "taxCode": {"maxLength": 20, "minLength": 0, "pattern": "^[a-zA-Z0-9@&-.()_,/]*$", "type": "string", "description": "<PERSON><PERSON> số thuế người nộp", "example": "**********"}, "taxTypeCode": {"maxLength": 10, "minLength": 0, "type": "string", "description": "<PERSON><PERSON> sắc thuế", "example": "VA"}, "taxTypeName": {"type": "string", "description": "<PERSON><PERSON><PERSON> thuế", "example": "<PERSON><PERSON><PERSON> giá trị gia tăng"}, "transDesc": {"type": "string", "description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> giao d<PERSON>ch", "example": "Tờ khai thuế GTGT"}, "treasuryCode": {"maxLength": 4, "minLength": 0, "type": "string", "description": "<PERSON>ã kho bạc", "example": "0022"}, "treasuryName": {"type": "string", "description": "<PERSON><PERSON><PERSON> kho bạc", "example": "<PERSON><PERSON> b<PERSON>c Nhà nước Hà Nội"}}, "description": "<PERSON><PERSON> s<PERSON>ch item"}, "ErrMsgDto": {"type": "object", "properties": {"code": {"type": "string"}, "field": {"type": "string"}, "message": {"type": "string"}}, "description": "<PERSON><PERSON> s<PERSON>ch lỗi", "example": "GOV0001, GOV0002"}, "ResultBatchCalcFeeRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/BatchCalcFeeRes"}}}, "BatchDetailEditReq": {"required": ["batchItemId", "transKey"], "type": "object", "properties": {"transKey": {"maxLength": 150, "minLength": 0, "type": "string", "description": "Key giao d<PERSON>ch", "example": "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86"}, "batchItemId": {"maxLength": 36, "minLength": 0, "type": "string", "description": "ID của item trong batch", "example": "a614d77b-9ee6-481f-b321-838ed39ddba8"}}}, "BatchDetailRes": {"type": "object", "properties": {"fileSize": {"type": "integer", "description": "<PERSON><PERSON><PERSON> file", "format": "int64", "example": 1000000}, "fileName": {"type": "string", "description": "Tên file", "example": "GOV_PAYMENT_BATCH_001"}, "batchNo": {"type": "string", "description": "Số lô", "example": "BDR20250607215402732"}, "batchType": {"type": "string", "description": "Loại lô: PAYMENT: <PERSON><PERSON><PERSON> thuế theo lô, INQUIRY: <PERSON><PERSON><PERSON> tin theo lô", "example": "PAYMENT"}, "validItems": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch item hợp lệ", "items": {"$ref": "#/components/schemas/BatchItemDetailDto"}}, "invalidItems": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch item không hợp lệ", "items": {"$ref": "#/components/schemas/BatchItemDetailDto"}}, "totalValidRecords": {"type": "integer", "format": "int32"}, "totalInvalidRecords": {"type": "integer", "format": "int32"}, "totalRecords": {"type": "integer", "format": "int32"}}, "description": "Data"}, "ResultBatchDetailRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/BatchDetailRes"}}}, "BatchConfirmReq": {"required": ["confirmValue", "transKey"], "type": "object", "properties": {"transKey": {"maxLength": 150, "minLength": 0, "type": "string", "description": "<PERSON><PERSON><PERSON> trị trả về từ API đẩy du<PERSON> (phục vụ truy vấn dữ liệu từ cache)", "example": "transKey"}, "confirmValue": {"type": "string", "description": "<PERSON><PERSON><PERSON> trị xác thực", "example": "confirmValue"}}}, "BatchProcessResultRes": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "totalSuccess": {"type": "integer", "format": "int64"}, "failTxns": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionResDetail"}}, "totalFail": {"type": "integer", "format": "int64"}}, "description": "Data"}, "ResultBatchProcessResultRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "messageArgs": {"type": "array", "description": "Message agruments", "items": {"type": "object", "description": "Message agruments"}}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/BatchProcessResultRes"}}}}}, "tags": ["governmentService"], "formated": "1"}