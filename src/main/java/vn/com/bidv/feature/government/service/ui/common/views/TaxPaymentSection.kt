package vn.com.bidv.feature.government.service.ui.common.views

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBSpacing.spacing2xs
import vn.com.bidv.designsystem.theme.IBSpacing.spacingNone
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.exts.toFormattedDate
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun TaxPaymentSection(items: List<TaxPaymentDMO>, onItemClick: (TaxPaymentDMO) -> Unit) {
    InfoCard(title = stringResource(R.string.thong_tin_khoan_nop)) {
        items.forEach { item ->
            TaxPaymentItemCard(item = item, onItemClick)
            Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        }
    }
}



@Composable
fun TaxPaymentItemCard(item: TaxPaymentDMO, onItemClick: (TaxPaymentDMO) -> Unit) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .border(
                width = IBBorderDivider.borderDividerS,
                color = curLocalColorScheme.borderMainSecondary,
                shape = RoundedCornerShape(size = spacingXs)
            )
            .fillMaxWidth()
            .background(
                color = curLocalColorScheme.bgMainTertiary,
                shape = RoundedCornerShape(size = spacingXs)
            )
            .padding(spacingNone)
            .wrapContentHeight(align = Alignment.CenterVertically)
            .clickable(
                indication = null,
                onClick = { onItemClick.invoke(item) },
                interactionSource = remember { MutableInteractionSource() }
            ),
        horizontalAlignment = Alignment.Start,
    ) {
        Text(
            text = item.declarationNo,
            color = curLocalColorScheme.contentMainPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = IBSpacing.spacingM,
                    top = IBSpacing.spacingS,
                    end = spacingXs,
                    bottom = IBSpacing.spacingS
                ),
            style = curLocalTypography.titleTitle_m,
            textAlign = TextAlign.Start,
        )

        HorizontalDivider(
            thickness = IBBorderDivider.borderDividerS,
            color = LocalColorScheme.current.borderMainPrimary
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingS)
        ) {
            Text(
                text = formatCodeName(item.eiTypeCode, item.eiTypeName),
                style = curLocalTypography.bodyBody_m,
            )
            Spacer(modifier = Modifier.height(spacing2xs))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = item.ecName.orEmpty(),
                    style = curLocalTypography.bodyBody_s,
                    color = curLocalColorScheme.contentMainTertiary,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .padding(spacingNone, spacingXs, spacingNone, spacingNone)
                    .fillMaxWidth()
            ) {
                Text(
                    text = item.amount.formatMoney(item.ccy, isShowCurrCode = true),
                    style = curLocalTypography.titleTitle_m
                )

                Spacer(modifier = Modifier.weight(1f))
                Icon(
                    painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.calendar_outline),
                    contentDescription = null,
                    tint = LocalColorScheme.current.contentMainTertiary,
                    modifier = Modifier.size(IBSpacing.spacingM)
                )
                Spacer(modifier = Modifier.width(spacing2xs))
                Text(
                    text = item.declarationDate.toFormattedDate(),
                    style = curLocalTypography.bodyBody_s,
                    color = curLocalColorScheme.contentMainTertiary
                )
            }
        }
    }
}

@Preview(showBackground = true, name = "Tax Payment Item Card Preview")
@Composable
fun PreviewTaxPaymentItemCard() {
    val item = Constants.fakeTransactions.first()
    TaxPaymentItemCard(item = item) {}
}


@Preview(showBackground = true, name = "Tax Payment Section Preview")
@Composable
fun PreviewTaxPaymentSection() {
    val items = Constants.fakeTransactions.take(2)
    TaxPaymentSection(items = items) {}
}