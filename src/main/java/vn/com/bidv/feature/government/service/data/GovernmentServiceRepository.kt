package vn.com.bidv.feature.government.service.data

import vn.com.bidv.feature.government.service.data.governmentservice.apis.GovernmentServiceApi
import vn.com.bidv.feature.government.service.data.governmentservice.model.AdministrativeAreaReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListAdministrativeAreaRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListChapterRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListCustomsCurrencyRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListEconomicContentRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListExportImportType
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListInquiryCustomsDutyRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListRevenueAccountRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListRevenueAuthorityRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTaxTypeRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTemplateListRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTreasuryRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTxnListRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.DataListTxnPendingListRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.ExportFileRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.InquiryCustomsDutyReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.ResultString
import vn.com.bidv.feature.government.service.data.governmentservice.model.RevenueAuthorityReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TemplateListReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TemplateSaveReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TreasuryDetailRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnConfirmReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDeleteReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnExportReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnInitPushReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnInitPushRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnListReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPrintDocumentReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnProcessResultRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnSaveReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.ValidateCustomsDutyReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.ValidateCustomsDutyRes
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class GovernmentServiceRepository @Inject constructor(
    private val service: GovernmentServiceApi,
) : BaseRepository() {

    suspend fun getListTransaction(
        request: TxnListReq
    ): NetworkResult<DataListTxnListRes> = launch {
        service.list(request)
    }

    suspend fun getListPendingTransaction(
        request: TxnPendingListReq
    ): NetworkResult<DataListTxnPendingListRes> = launch {
        service.listPending(request)
    }

    suspend fun deleteTransaction(
        request: TxnDeleteReq
    ): NetworkResult<ResultString> = launch {
        service.delete(request)
    }

    suspend fun initPushTransaction(
        request: TxnInitPushReq
    ): NetworkResult<TxnInitPushRes> = launch {
        service.initPush(request)
    }

    suspend fun confirmPushTransaction(
        request: TxnConfirmReq
    ): NetworkResult<TxnProcessResultRes> = launch {
        service.confirmPush(request)
    }

    suspend fun getPendingTransactionDetail(txnDetailReq: TxnDetailReq): NetworkResult<TxnDetailRes> =
        launch { service.detail(txnDetailReq) }

    suspend fun inquiryTransaction(request: InquiryCustomsDutyReq): NetworkResult<DataListInquiryCustomsDutyRes> =
        launch { service.inquiry(request) }

    suspend fun getListChapterCode(): NetworkResult<DataListChapterRes> = launch {
        service.listChapter()
    }

    suspend fun getListEconomicCode(): NetworkResult<DataListEconomicContentRes> = launch {
        service.listEconomicContent()
    }

    suspend fun getListTaxType(): NetworkResult<DataListTaxTypeRes> = launch {
        service.listTaxType()
    }

    suspend fun getListCustomsCurrency(): NetworkResult<DataListCustomsCurrencyRes> = launch {
        service.listCustomsCurrency()
    }

    suspend fun getListTradeType(): NetworkResult<DataListExportImportType> = launch {
        service.listExportImportType()
    }

    suspend fun getListTreasury(): NetworkResult<DataListTreasuryRes> = launch {
        service.listTreasury()
    }

    suspend fun getTreasuryDetail(request: AdministrativeAreaReq): NetworkResult<TreasuryDetailRes> = launch {
        service.detailTreasury(request)
    }

    suspend fun getListRevenueAuthority(request: RevenueAuthorityReq): NetworkResult<DataListRevenueAuthorityRes> =
        launch {
            service.listRevenueAuthority(request)
        }

    suspend fun getListRevenueAccount(): NetworkResult<DataListRevenueAccountRes> = launch {
        service.listRevenueAccount()
    }

    suspend fun getListAdministrativeArea(request: AdministrativeAreaReq): NetworkResult<DataListAdministrativeAreaRes> =
        launch {
            service.listAdministrativeArea(request)
        }

    suspend fun saveTransaction(request: TxnSaveReq): NetworkResult<ResultString> = launch {
        service.save(request)
    }

    suspend fun validateCustomDutiesForm(validateCustomsDutyReq: ValidateCustomsDutyReq): NetworkResult<ValidateCustomsDutyRes> =
        launch {
            service.validate(validateCustomsDutyReq)
        }

    suspend fun saveTemplateTransaction(request: TemplateSaveReq): NetworkResult<ResultString> =
        launch {
            service.save1(request)
        }

    suspend fun getListTransTemp(request: TemplateListReq): NetworkResult<DataListTemplateListRes> =
        launch {
            service.list1(request)
        }

    suspend fun saveEditingTransaction(request: TxnSaveReq): NetworkResult<ResultString> = launch {
        service.edit(request)
    }

    suspend fun printDocument(request: TxnPrintDocumentReq): NetworkResult<ExportFileRes> = launch {
        service.print(request)
    }

    suspend fun exportTransaction(request: TxnExportReq): NetworkResult<ExportFileRes> = launch {
        service.export(request)
    }

    suspend fun updateTransactionStatus(request: TxnDetailReq): NetworkResult<TxnDetailRes> = launch {
        service.updateStatus(request)
    }
}
