package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import vn.com.bidv.feature.government.service.domain.model.CustomsCurrencyDMO
import vn.com.bidv.feature.government.service.ui.common.CommonDropDown
import vn.com.bidv.feature.government.service.ui.common.CommonSearchDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentReducer
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
fun CustomsCurrencyDropDown(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
) {
    CommonDropDown(
        labelText = stringResource(R.string.loai_tien_hq),
        selectedItem = uiState.customsCurrencySelected,
        displayTextSelector = {
            formatCodeName(it?.ccCode, it?.ccName)
        },
        fieldError = uiState.fieldError[AddPaymentItem.DropDownItem.CustomsCurrency],
        onClickEnd = {
            onEvent(
                AddPaymentReducer.AddPaymentEvent.CustomsCurrencyEvent.ShowCustomsCurrencyBottomSheet(
                    true
                )
            )
        },
        onClickClear = {
            onEvent(AddPaymentReducer.AddPaymentEvent.CustomsCurrencyEvent.ClearCustomsCurrency)
        }
    )
}

@Composable
fun ShowCustomsCurrencyBottomSheet(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    onValueChange: (AddPaymentItem.DropDownItem.CustomsCurrency, CustomsCurrencyDMO?) -> Unit
) {
    if (uiState.showCustomsCurrencyBottomSheet) {
        CommonSearchDialog(
            title = stringResource(R.string.loai_tien_hq),
            itemSelected = uiState.customsCurrencySelected,
            listData = uiState.listCustomsCurrency,
            showSearchBox = true,
            searchFilter = { item, query ->
                val search = "${item.ccCode}${item.ccName}"
                VNCharacterUtil.removeAccent(search)
                    .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
            },
            onDismiss = {
                onEvent(
                    AddPaymentReducer.AddPaymentEvent.CustomsCurrencyEvent.ShowCustomsCurrencyBottomSheet(
                        false
                    )
                )
                onValueChange(AddPaymentItem.DropDownItem.CustomsCurrency, it)
            },
            itemTitleSelector = { formatCodeName(it.ccCode, it.ccName) },
            compareKey = { a, b -> a.ccCode == b?.ccCode }
        )
    }
}
