package vn.com.bidv.feature.government.service.domain.model

import com.google.gson.annotations.SerializedName

/**
 * Data
 *
 * @param treasuryCode
 * @param benBankCode
 * @param benBankName
 * @param isInBidv
 */

data class TreasuryDetailDMO(

    @SerializedName("treasuryCode")
    val treasuryCode: String? = null,

    @SerializedName("benBankCode")
    val benBankCode: String? = null,

    @SerializedName("benBankName")
    val benBankName: String? = null,

    @SerializedName("isInBidv")
    val isInBidv: Boolean? = null

) {

}