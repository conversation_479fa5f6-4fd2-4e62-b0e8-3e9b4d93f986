package vn.com.bidv.feature.government.service.domain.usecase

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.AdministrativeAreaReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.Page
import vn.com.bidv.feature.government.service.data.governmentservice.model.RevenueAuthorityReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TemplateListReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TemplateSaveReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnListReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnSaveReq
import vn.com.bidv.feature.government.service.domain.model.AdministrativeAreaDMO
import vn.com.bidv.feature.government.service.domain.model.BaseDataListDMO
import vn.com.bidv.feature.government.service.domain.model.ChapterDMO
import vn.com.bidv.feature.government.service.domain.model.CustomsCurrencyDMO
import vn.com.bidv.feature.government.service.domain.model.EconomicContentDMO
import vn.com.bidv.feature.government.service.domain.model.ExportImportTypeDMO
import vn.com.bidv.feature.government.service.domain.model.ResultStringDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAuthorityDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.feature.government.service.domain.model.TaxTypeDMO
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO
import vn.com.bidv.feature.government.service.domain.model.TransactionTemplateDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDetailDMO
import vn.com.bidv.feature.government.service.domain.model.TxnListDMO
import vn.com.bidv.feature.government.service.domain.model.TxnPendingListDMO
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD
import vn.com.bidv.sdkbase.utils.convert
import java.lang.reflect.Type
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import javax.inject.Inject

class GovernmentServiceUseCase @Inject constructor(
    private val governmentServiceRepository: GovernmentServiceRepository,
    private val localRepository: LocalRepository
) {
    suspend fun getListTransaction(
        rule: ListCustomsDutiesRuleFilter?,
        pageIndex: Int,
        pageSize: Int
    ): DomainResult<BaseDataListDMO<TxnListDMO>> {
        val today = LocalDate.now()
        val dateFormat = DateTimeFormatter.ofPattern(FORMAT_YYYY_MM_DD)
        val request = TxnListReq(
            page = Page(
                pageNum = pageIndex,
                pageSize = pageSize
            ),
            search = rule?.search,
            startDate = rule?.startDate ?: today.minusDays(Constants.DEFAULT_DAYS_TO_SEARCH)
                .format(dateFormat),
            endDate = rule?.endDate ?: today.format(dateFormat),
            minAmount = rule?.minAmount,
            maxAmount = rule?.maxAmount,
            ccys = rule?.listCurrencySelected.takeIf { it?.size != rule?.totalListCurrency },
            statuses = rule?.listStatusSelected.takeIf { it?.size != rule?.totalListStatus },
            debitAccNo = rule?.debit,
            taxCode = rule?.tax,
            declarationNo = rule?.declaration,
            batchNo = rule?.batch
        )

        val result = governmentServiceRepository.getListTransaction(request)
        val type: Type = object : TypeToken<BaseDataListDMO<TxnListDMO>>() {}.type
        return result.convert(type)
    }

    suspend fun getListPendingTransaction(
        rule: ListCustomsDutiesRuleFilter?,
        pageIndex: Int,
        pageSize: Int
    ): DomainResult<BaseDataListDMO<TxnPendingListDMO>> {

        val request = TxnPendingListReq(
            page = Page(
                pageNum = pageIndex,
                pageSize = pageSize
            ),
            search = rule?.search,
            startDate = rule?.startDate,
            endDate = rule?.endDate,
            minAmount = rule?.minAmount,
            maxAmount = rule?.maxAmount,
            ccys = rule?.listCurrencySelected,
            statuses = rule?.listStatusSelected,
            debitAccNo = rule?.debit,
            taxCode = rule?.tax,
            declarationNo = rule?.declaration,
            batchNo = rule?.batch
        )

        val result = governmentServiceRepository.getListPendingTransaction(request)
        val type: Type = object : TypeToken<BaseDataListDMO<TxnPendingListDMO>>() {}.type
        return result.convert(type)
    }

    fun getListCurrency(): DomainResult<List<String>> {
        return DomainResult.Success(listOf("VND"))
    }

    fun getListStatus(): DomainResult<List<TransactionStatusBase>> {
        return DomainResult.Success(
            listOf(
                TransactionStatusBase.INIT,
                TransactionStatusBase.REJECTED,
                TransactionStatusBase.PENDING_APPROVAL,
                TransactionStatusBase.SUCCESS,
                TransactionStatusBase.CANCELLED,
                TransactionStatusBase.UNDEFINED,
                TransactionStatusBase.FAILED,
                TransactionStatusBase.BANK_PROCESSING,
            )
        )
    }

    fun getListPendingStatus(): DomainResult<List<TransactionStatusBase>> {
        return DomainResult.Success(
            listOf(TransactionStatusBase.INIT, TransactionStatusBase.REJECTED)
        )
    }

    suspend fun getListChapterCode(): DomainResult<BaseDataListDMO<ChapterDMO>> {
        val result = governmentServiceRepository.getListChapterCode()
        val type: Type = object : TypeToken<BaseDataListDMO<ChapterDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun getListEconomicCode(): DomainResult<BaseDataListDMO<EconomicContentDMO>> {
        val result = governmentServiceRepository.getListEconomicCode()
        val type: Type = object : TypeToken<BaseDataListDMO<EconomicContentDMO>>() {}.type

        return result.convert(type)
    }

    fun getListCurrencyType(): List<String> {
        return listOf("VND")
    }

    suspend fun getListTaxType(): DomainResult<BaseDataListDMO<TaxTypeDMO>> {
        val result = governmentServiceRepository.getListTaxType()
        val type: Type = object : TypeToken<BaseDataListDMO<TaxTypeDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun getListCustomsCurrency(): DomainResult<BaseDataListDMO<CustomsCurrencyDMO>> {
        val result = governmentServiceRepository.getListCustomsCurrency()
        val type: Type = object : TypeToken<BaseDataListDMO<CustomsCurrencyDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun getListTradeType(): DomainResult<BaseDataListDMO<ExportImportTypeDMO>> {
        val result = governmentServiceRepository.getListTradeType()
        val type: Type = object : TypeToken<BaseDataListDMO<ExportImportTypeDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun getListTreasury(): DomainResult<BaseDataListDMO<TreasuryDMO>> {
        val result = governmentServiceRepository.getListTreasury()
        val type: Type = object : TypeToken<BaseDataListDMO<TreasuryDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun getTreasuryDetail(treasuryCode: String): DomainResult<TreasuryDetailDMO> {
        val result = governmentServiceRepository.getTreasuryDetail(
            AdministrativeAreaReq(
                treasuryCode = treasuryCode
            )
        )
        return result.convert(TreasuryDetailDMO::class.java)
    }

    suspend fun getListRevenueAuthority(treasuryCode: String): DomainResult<BaseDataListDMO<RevenueAuthorityDMO>> {
        val result = governmentServiceRepository.getListRevenueAuthority(
            RevenueAuthorityReq(treasuryCode = treasuryCode)
        )
        val type: Type = object : TypeToken<BaseDataListDMO<RevenueAuthorityDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun getListRevenueAccount(): DomainResult<BaseDataListDMO<RevenueAccountDMO>> {
        val result = governmentServiceRepository.getListRevenueAccount()
        val type: Type = object : TypeToken<BaseDataListDMO<RevenueAccountDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun getListAdministrativeArea(treasuryCode: String?): DomainResult<BaseDataListDMO<AdministrativeAreaDMO>> {
        val result = governmentServiceRepository.getListAdministrativeArea(
            AdministrativeAreaReq(treasuryCode = treasuryCode)
        )
        val type: Type = object : TypeToken<BaseDataListDMO<AdministrativeAreaDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun sendDataToMain(isEditPayment: Boolean = false, taxPaymentDMO: TaxPaymentDMO?) {
        val json = Gson().toJson(taxPaymentDMO)
        localRepository.shareDataTo(
            if (isEditPayment) Constants.EDIT_PAYMENT_DATA else Constants.ADD_PAYMENT_DATA,
            ShareDataDTO(
                if (isEditPayment) Constants.EDIT_PAYMENT_DATA else Constants.ADD_PAYMENT_DATA,
                json
            )
        )
    }

    suspend fun saveTemplateTransaction(request: TemplateSaveReq): DomainResult<String> {
        val domain = governmentServiceRepository.saveTemplateTransaction(request)
        val result = domain.convert {
            this.data.orEmpty()
        }
        return result
    }

    suspend fun saveTransaction(request: TxnSaveReq): DomainResult<ResultStringDMO> {
        val result = governmentServiceRepository.saveTransaction(request)
        return result.convert(ResultStringDMO::class.java)
    }

    suspend fun getListTransTemp(request: TemplateListReq): DomainResult<BaseDataListDMO<TransactionTemplateDMO>> {
        val result = governmentServiceRepository.getListTransTemp(request)
        val type: Type = object : TypeToken<BaseDataListDMO<TransactionTemplateDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun saveEditingTransaction(request: TxnSaveReq): DomainResult<ResultStringDMO> {
        val result = governmentServiceRepository.saveEditingTransaction(request)
        return result.convert(ResultStringDMO::class.java)
    }

    suspend fun updateTransactionStatus(
        transactionId: String,
    ): DomainResult<TransactionDetailDMO> {
        val result = governmentServiceRepository.updateTransactionStatus(
            TxnDetailReq(
                txnId = transactionId
            )
        )
        return result.convert(TransactionDetailDMO::class.java)
    }
}