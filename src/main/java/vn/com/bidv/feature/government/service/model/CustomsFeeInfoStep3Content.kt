package vn.com.bidv.feature.government.service.model

import android.content.Context
import vn.com.bidv.feature.government.service.domain.model.AdministrativeAreaDMO
import vn.com.bidv.feature.government.service.domain.model.BalanceAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAuthorityDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO
import vn.com.bidv.feature.government.service.domain.model.ValidateTxnDMO

data class CustomsFeeInfoStep3Content(
    val taxPayerInfo: TaxPayerInfo,
    val taxDelegatorInfo: TaxPayerInfo?,
    val listPayment: List<TaxPaymentDMO>,
    val debitAccount: BalanceAccountDMO,
    val treasury: TreasuryDMO,
    val revenueAuthority: RevenueAuthorityDMO,
    val revenueAccount: RevenueAccountDMO,
    val administrativeArea: AdministrativeAreaDMO,
    val taxPayerEntity: TaxPayerEntity,
    val noteToApprover: String,
    val priorityTransaction: Boolean,
    val customerTransactionCode: String,
    val benBankCode: String,
    val benBankName: String,
    val validateResult: ValidateTxnDMO,
    val isEditingTransaction: Boolean,
) {
    companion object {
        fun empty() = CustomsFeeInfoStep3Content(
            taxPayerInfo = TaxPayerInfo("", "", ""),
            taxDelegatorInfo = null,
            listPayment = listOf(),
            debitAccount = BalanceAccountDMO("", "", "", ""),
            treasury = TreasuryDMO("", ""),
            revenueAuthority = RevenueAuthorityDMO("", ""),
            revenueAccount = RevenueAccountDMO("", ""),
            administrativeArea = AdministrativeAreaDMO("", ""),
            taxPayerEntity = TaxPayerEntity.BUSINESS,
            noteToApprover = "",
            validateResult = ValidateTxnDMO(),
            isEditingTransaction = false,
            priorityTransaction = false,
            customerTransactionCode = "",
            benBankCode = "",
            benBankName = "",
        )
    }
}

fun CustomsFeeInfoStep3Content.toTransactionDetailDMO(context: Context): TransactionDetailDMO {
    return TransactionDetailDMO(
        transactionId = "",
        taxItems = listPayment,
        paymentAmount = validateResult.amount,
        ccy = validateResult.ccy,
        paymentAmountText = validateResult.amountText,
        feeCcy = validateResult.feeCcy,
        feeTotal = validateResult.feeTotal,
        feeMethod = validateResult.feeOpt,
        authorityCode = revenueAuthority.revAuthCode,
        authorityName = revenueAuthority.revAuthName,
        revAccCode = revenueAccount.revAccCode,
        revAccName = revenueAccount.revAccName,
        debitAccNumber = debitAccount.accountNo,
        debitAccName = debitAccount.accountName,
        treasuryName = treasury.treasuryName,
        admAreaName = administrativeArea.admAreaName,
        admAreaCode = administrativeArea.admAreaCode,
        noteToAuthorizer = noteToApprover,
        priority = priorityTransaction,
        orgId = customerTransactionCode,
        payerTaxCode = taxPayerInfo.taxId,
        payerName = taxPayerInfo.name,
        payerAddress = taxPayerInfo.address,
        altTaxCode = taxDelegatorInfo?.taxId,
        altPayerName = taxDelegatorInfo?.name,
        altPayerAddress = taxDelegatorInfo?.address,
        payerType = taxPayerEntity.value,
        payerTypeName = context.getString(taxPayerEntity.getDisplayName()),
        benBankCode = benBankCode,
        benBankName = benBankName,
    )
}