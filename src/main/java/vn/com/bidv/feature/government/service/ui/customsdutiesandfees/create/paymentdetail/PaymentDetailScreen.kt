package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.paymentdetail

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.IBankSectionHeader
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.LeadingType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.paymentdetail.PaymentDetailReducer.PaymentDetailViewEvent
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.paymentdetail.PaymentDetailReducer.PaymentDetailViewState
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.exts.toFormattedDate
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun PaymentDetailScreen(
    taxPaymentData: TaxPaymentDMO,
    navController: NavHostController,
    viewModel: PaymentDetailViewModel = hiltViewModel()
) {
    val curLocalColorScheme = LocalColorScheme.current

    val topAppBarConfig = TopAppBarConfig(
        isShowTopAppBar = true,
        isShowNavigationIcon = true,
        titleTopAppBar = stringResource(R.string.chi_tiet_khoan_nop),
        titleTopAppBarColor = curLocalColorScheme.bgNon_opaqueInverse_pressed
    )

    LaunchedEffect(taxPaymentData) {
        viewModel.sendEvent(PaymentDetailViewEvent.InitializeData(taxPaymentData))
    }

    BaseScreen(
        navController = navController,
        viewModel = viewModel,
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = topAppBarConfig,
        backgroundColor = curLocalColorScheme.bgMainSecondary
    ) { uiState, _ ->
        PaymentDetailContent(uiState = uiState)
    }
}

@Composable
private fun PaymentDetailContent(uiState: PaymentDetailViewState) {
    if (uiState.isInitialized && uiState.taxPaymentData != null) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = IBSpacing.spacingM)
        ) {
            item {
                PaymentInfoSection(uiState.taxPaymentData)
                Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            }

            item {
                BeneficiaryInfoSection(uiState.taxPaymentData)
                Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            }
        }
    }
}

@Composable
private fun PaymentInfoSection(data: TaxPaymentDMO) {
    val items = listOf(
        stringResource(R.string.so_to_khaiqdtb) to data.declarationNo,
        stringResource(R.string.ngay_to_khaiqdtb) to data.declarationDate.toFormattedDate(SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD),
        stringResource(R.string.ma_chuong) to formatCodeName(data.chapterCode, data.chapterName),
        stringResource(R.string.ma_noi_dung_kinh_ke) to formatCodeName(data.ecCode,data.ecName),
        stringResource(R.string.so_tien) to data.amount.formatMoney(data.ccy, isShowCurrCode = true),
        stringResource(R.string.loai_tien) to data.ccy,
        stringResource(R.string.dien_giai_giao_dich) to data.transDesc,
        stringResource(R.string.sac_thue) to formatCodeName(data.taxTypeCode, data.taxTypeName),
        stringResource(R.string.loai_tien_hq) to formatCodeName(data.ccCode, data.ccName),
        stringResource(R.string.loai_hinh_xnk) to formatCodeName(data.eiTypeCode, data.eiTypeName)
    )

    InfoCard(title = stringResource(R.string.thong_tin_khoan_nop)) {
        for ((label, value) in items) {
            DetailItem(label = label, value = value)
        }
    }
}

@Composable
private fun BeneficiaryInfoSection(data: TaxPaymentDMO) {
    val items = listOf(
        stringResource(R.string.ma_va_ten_kho_bac) to formatCodeName(data.treasuryCode, data.treasuryName),
        stringResource(R.string.co_quan_thu) to formatCodeName(data.revAuthCode, data.revAuthName),
        stringResource(R.string.tai_khoan_thu_nsnn) to formatCodeName(data.revAccCode, data.revAccName),
        stringResource(R.string.dia_ban_hanh_chinh) to (data.admAreaCode ?: "--"),
        stringResource(R.string.ten_dia_ban_hanh_chinh) to (data.admAreaName ?: "--"),
        stringResource(R.string.loai_hinh_nnt) to (data.payerTypeName ?: "--")
    )

    InfoCard(title = stringResource(R.string.thong_tin_thu_huong)) {
        for ((label, value) in items) {
            DetailItem(label = label, value = value)
        }
    }
}

@Composable
private fun formatCodeName(code: String?, name: String?): String {
    return GovernmentServiceUtils.formatCodeName(
        code = code,
        name = name,
        emptyString = "--"
    )
}

@Composable
private fun InfoCard(
    title: String,
    body: @Composable () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = LocalColorScheme.current.bgMainTertiary,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
            )
    ) {
        IBankSectionHeader(
            shLeadingType = LeadingType.Dash(),
            shSectionTitle = title,
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = IBSpacing.spacingM)
        ) {
            body()
        }
    }
}

@Composable
private fun DetailItem(label: String, value: String) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = IBSpacing.spacingXs),
    ) {
        Text(
            text = label,
            style = curLocalTypography.bodyBody_m,
            color = curLocalColorScheme.contentMainTertiary
        )
        Text(
            text = value,
            style = curLocalTypography.titleTitle_m,
            color = curLocalColorScheme.contentMainPrimary
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewTaxPaymentDetailScreen() {
    val mockData = Constants.fakeTransactions.first()

    PaymentInfoSection(mockData)
}
