package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import vn.com.bidv.feature.government.service.domain.model.RevenueAuthorityDMO
import vn.com.bidv.feature.government.service.model.FieldStatus
import vn.com.bidv.feature.government.service.model.TaxPaymentFormField
import vn.com.bidv.feature.government.service.model.TaxPaymentFormFieldValidateError
import vn.com.bidv.feature.government.service.ui.common.CommonDropDown
import vn.com.bidv.feature.government.service.ui.common.CommonSearchDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.TaxPaymentInfoUiState
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.TaxPaymentInfoViewEvent
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
internal fun RevenueAuthorityDropdown(
    uiState: TaxPaymentInfoUiState,
    onEvent: (TaxPaymentInfoViewEvent) -> Unit,
) {
    CommonDropDown(
        required = true,
        showClearButton = false,
        labelText = stringResource(R.string.co_quan_thu),
        selectedItem = uiState.revenueAuthoritySelected,
        displayTextSelector = { formatCodeName(it?.revAuthCode, it?.revAuthName) },
        fieldError = if (uiState.formError == TaxPaymentFormFieldValidateError.RequiredFieldNotFilled && uiState.revenueAuthoritySelected == null) {
            FieldStatus.INVALID
        } else null,
        enabled = uiState.treasurySelected != null,
        onClickClear = {
            onEvent(TaxPaymentInfoViewEvent.UpdateSelectedRevenueAuthority(null))
        },
        onClickEnd = {
            onEvent(
                TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
                    TaxPaymentFormField.REV_AUTH_CODE,
                    true
                )
            )
        }
    )
}
@Composable
internal fun RevenueAuthoritySearchDialog(
    listData: List<RevenueAuthorityDMO>,
    itemSelected: RevenueAuthorityDMO?,
    onDismiss: (RevenueAuthorityDMO?) -> Unit,
) {
    CommonSearchDialog(
        title = stringResource(R.string.co_quan_thu),
        itemSelected = itemSelected,
        listData = listData,
        showSearchBox = true,
        searchFilter = { item, query ->
            val search = "${item.revAuthCode}${item.revAuthName}"
            VNCharacterUtil.removeAccent(search)
                .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
        },
        onDismiss = onDismiss,
        itemTitleSelector = { formatCodeName(it.revAuthCode, it.revAuthName) }
    )
}