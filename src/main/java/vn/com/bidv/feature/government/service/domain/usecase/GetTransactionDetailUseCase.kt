package vn.com.bidv.feature.government.service.domain.usecase

import vn.com.bidv.common.extenstion.isNull
import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailReq
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import java.util.UUID
import javax.inject.Inject

class GetTransactionDetailUseCase @Inject constructor(
    private val governmentServiceRepository: GovernmentServiceRepository
) {
    suspend operator fun invoke(transactionId: String): DomainResult<TransactionDetailDMO> {
        val detailRequest = TxnDetailReq(txnId = transactionId)
        val networkResult = governmentServiceRepository.getPendingTransactionDetail(detailRequest)
        val result = networkResult.convert(TransactionDetailDMO::class.java) { _, to ->
            to?.copy(
                taxItems = to.taxItems?.map {
                    if (it.uniqueId.isNull()) it.copy(
                        uniqueId = UUID.randomUUID().toString()
                    ) else it
                }
            )
        }
        return result
    }
}