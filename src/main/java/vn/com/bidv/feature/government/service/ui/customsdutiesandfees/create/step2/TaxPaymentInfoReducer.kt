package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2

import vn.com.bidv.common.extenstion.isNotNull
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.government.service.domain.model.AdministrativeAreaDMO
import vn.com.bidv.feature.government.service.domain.model.BalanceAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAuthorityDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDetailDMO
import vn.com.bidv.feature.government.service.domain.model.ValidateTxnDMO
import vn.com.bidv.feature.government.service.model.CustomsFeeInfoStep2Content
import vn.com.bidv.feature.government.service.model.CustomsFeeInfoStep3Content
import vn.com.bidv.feature.government.service.model.TaxPayerEntity
import vn.com.bidv.feature.government.service.model.TaxPayerInfo
import vn.com.bidv.feature.government.service.model.TaxPaymentApiCall
import vn.com.bidv.feature.government.service.model.TaxPaymentFormField
import vn.com.bidv.feature.government.service.model.TaxPaymentFormFieldValidateError
import vn.com.bidv.feature.government.service.model.TaxPaymentValidateFailedPopupType
import kotlin.reflect.KProperty1

data class TaxPaymentInfoUiState(
    val isInitialized: Boolean = false,
    val taxPayerInfo: TaxPayerInfo? = null,
    val taxDelegatorInfo: TaxPayerInfo? = null,

    val listDebitAccounts: List<BalanceAccountDMO>? = null,
    val listPayment: List<TaxPaymentDMO> = listOf(),
    val listTreasury: List<TreasuryDMO>? = null,
    val listRevenueAuthority: List<RevenueAuthorityDMO>? = null,
    val listRevenueAccount: List<RevenueAccountDMO>? = null,
    val listAdministrativeArea: List<AdministrativeAreaDMO>? = null,

    val debitAccountSelected: BalanceAccountDMO? = null,
    val treasurySelected: TreasuryDMO? = null,
    val revenueAuthoritySelected: RevenueAuthorityDMO? = null,
    val revenueAccountSelected: RevenueAccountDMO? = null,
    val administrativeAreaSelected: AdministrativeAreaDMO? = null,
    val taxPayerEntitySelected: TaxPayerEntity = TaxPayerEntity.BUSINESS,

    val showDebitAccountBottomSheet: Boolean = false,
    val showTreasuryBottomSheet: Boolean = false,
    val showRevenueAuthorityBottomSheet: Boolean = false,
    val showRevenueAccountBottomSheet: Boolean = false,
    val showAdministrativeAreaBottomSheet: Boolean = false,
    val showTaxPayerEntityBottomSheet: Boolean = false,

    val noteToApprover: String = "",
    val priorityTransaction: Boolean = false,
    val customerTransactionCode: String = "",
    val formError: TaxPaymentFormFieldValidateError? = null,

    val benBankCode: String = "",
    val benBankName: String = "",
    val editingTransactionId: String? = null,
) : ViewState {
    fun clearData(): TaxPaymentInfoUiState = TaxPaymentInfoUiState()
}

sealed interface TaxPaymentInfoViewEvent : ViewEvent {
    data class InitScreen(
        val step2Content: CustomsFeeInfoStep2Content,
    ) : TaxPaymentInfoViewEvent

    data class UpdateTreasuryList(val treasuryList: List<TreasuryDMO>) : TaxPaymentInfoViewEvent
    data class UpdateRevenueAuthorityList(val revenueAuthorityList: List<RevenueAuthorityDMO>) :
        TaxPaymentInfoViewEvent

    data class UpdateRevenueAccountList(val revenueAccountList: List<RevenueAccountDMO>) :
        TaxPaymentInfoViewEvent

    data class UpdateAdministrativeAreaList(val administrativeAreaList: List<AdministrativeAreaDMO>) :
        TaxPaymentInfoViewEvent

    data class UpdateDebitAccountList(
        val debitAccountList: List<BalanceAccountDMO>,
        val shouldShowBottomSheet: Boolean
    ) : TaxPaymentInfoViewEvent

    data class UpdateSelectedDebitAccount(val debitAccount: BalanceAccountDMO?) :
        TaxPaymentInfoViewEvent

    data class UpdateSelectedTreasury(val treasury: TreasuryDMO?) : TaxPaymentInfoViewEvent
    data class UpdateSelectedRevenueAuthority(val revenueAuthority: RevenueAuthorityDMO?) :
        TaxPaymentInfoViewEvent

    data class UpdateSelectedRevenueAccount(val revenueAccount: RevenueAccountDMO?) :
        TaxPaymentInfoViewEvent

    data class UpdateSelectedAdministrativeArea(val administrativeArea: AdministrativeAreaDMO?) :
        TaxPaymentInfoViewEvent

    data class UpdateSelectedTaxPayerEntity(val taxPayerEntity: TaxPayerEntity) :
        TaxPaymentInfoViewEvent

    data class UpdateNoteToApproverString(val value: String) : TaxPaymentInfoViewEvent
    data class UpdateCustomerTransactionCode(val value: String) : TaxPaymentInfoViewEvent

    data class UpdateBenBankInfo(val treasury: TreasuryDetailDMO) : TaxPaymentInfoViewEvent
    data class TogglePriorityTransaction(val priorityTransaction: Boolean) : TaxPaymentInfoViewEvent

    data class ToggleBottomSheetVisibility(val field: TaxPaymentFormField, val show: Boolean) :
        TaxPaymentInfoViewEvent

    data class OnCallApiError(
        val errorCode: String?,
        val errorMessage: String?,
        val type: TaxPaymentApiCall
    ) : TaxPaymentInfoViewEvent

    data object ValidateBeforeContinue : TaxPaymentInfoViewEvent
    data class RetryFetchData(val type: TaxPaymentApiCall) : TaxPaymentInfoViewEvent
    data class ValidateServerSideSuccess(val validateCustomsDutyDMO: ValidateTxnDMO) :
        TaxPaymentInfoViewEvent, UIEffect

    data class DeletePayment(val payment: TaxPaymentDMO) : TaxPaymentInfoViewEvent
    data class UpdatePaymentItem(val data: TaxPaymentDMO) : TaxPaymentInfoViewEvent
    data object ClearData : TaxPaymentInfoViewEvent
}

sealed interface TaxPaymentInfoSideEffect : SideEffect {
    data class InitScreen(val treasury: TreasuryDMO?, val shouldFetchTreasuryDetail: Boolean) :
        TaxPaymentInfoSideEffect

    data class GetListDebitAccount(val shouldShowBottomSheet: Boolean) : TaxPaymentInfoSideEffect
    data class CheckAndSetDebitAccount(val debitAccount: BalanceAccountDMO) : TaxPaymentInfoSideEffect
    data object GetListTreasury : TaxPaymentInfoSideEffect
    data class GetListRevenueAuthority(val treasury: TreasuryDMO) : TaxPaymentInfoSideEffect
    data object GetListRevenueAccount : TaxPaymentInfoSideEffect
    data object GetListAdministrativeArea : TaxPaymentInfoSideEffect
    data class GetTreasuryDetail(val treasury: TreasuryDMO) : TaxPaymentInfoSideEffect

    data class OnCallApiError(
        val errorCode: String?,
        val errorMessage: String?,
        val type: TaxPaymentApiCall
    ) : TaxPaymentInfoSideEffect, UIEffect

    data object ValidateClientSideSuccess : TaxPaymentInfoSideEffect
    data object RetryValidateServerSide : TaxPaymentInfoSideEffect
    data class ValidateServerSideSuccess(val customsFeeInfoStep3Content: CustomsFeeInfoStep3Content) :
        TaxPaymentInfoSideEffect, UIEffect

    data class ValidateBeforeContinueFailed(val popupType: TaxPaymentValidateFailedPopupType) :
        TaxPaymentInfoSideEffect, UIEffect

    data class DeletePaymentSuccess(val shouldPopToPreviousStep: Boolean) :
        TaxPaymentInfoSideEffect, UIEffect
}

class TaxPaymentInfoReducer :
    Reducer<TaxPaymentInfoUiState, TaxPaymentInfoViewEvent, TaxPaymentInfoSideEffect> {
    override fun reduce(
        previousState: TaxPaymentInfoUiState,
        event: TaxPaymentInfoViewEvent
    ): Pair<TaxPaymentInfoUiState, TaxPaymentInfoSideEffect?> {
        return when (event) {
            is TaxPaymentInfoViewEvent.InitScreen -> {
                val (listPayment, taxPayerInfo, taxDelegatorInfo,
                    debitAccountSelected, treasurySelected, revenueAuthoritySelected,
                    revenueAccountSelected, administrativeAreaSelected, taxPayerEntitySelected,
                    noteToApprover, priorityTransaction, customerTransactionCode,
                    benBankCode, benBankName, editingTransactionId
                ) = event.step2Content

                val state = previousState.copy(
                    listPayment = listPayment,
                    taxPayerInfo = taxPayerInfo,
                    taxDelegatorInfo = taxDelegatorInfo,
                    isInitialized = true,
                    debitAccountSelected = debitAccountSelected,
                    treasurySelected = treasurySelected ?: getDefaultFieldFromPayments(
                        listPayment,
                        TaxPaymentDMO::treasuryCode
                    ) {
                        TreasuryDMO(it.treasuryCode, it.treasuryName)
                    },
                    revenueAuthoritySelected = revenueAuthoritySelected
                        ?: getDefaultFieldFromPayments(
                            listPayment,
                            TaxPaymentDMO::revAuthCode
                        ) {
                            RevenueAuthorityDMO(it.revAuthCode, it.revAuthName)
                        },
                    revenueAccountSelected = revenueAccountSelected ?: getDefaultFieldFromPayments(
                        listPayment,
                        TaxPaymentDMO::revAccCode
                    ) {
                        RevenueAccountDMO(it.revAccCode, it.revAccName)
                    },
                    administrativeAreaSelected = administrativeAreaSelected
                        ?: getDefaultFieldFromPayments(
                            listPayment,
                            TaxPaymentDMO::admAreaCode
                        ) {
                            AdministrativeAreaDMO(it.admAreaCode, it.admAreaName)
                        },
                    taxPayerEntitySelected = taxPayerEntitySelected,
                    noteToApprover = noteToApprover ?: previousState.noteToApprover,
                    priorityTransaction = priorityTransaction ?: previousState.priorityTransaction,
                    customerTransactionCode = customerTransactionCode
                        ?: previousState.customerTransactionCode,
                    editingTransactionId = editingTransactionId,
                    benBankCode = benBankCode ?: getDefaultFieldFromPayments(
                        listPayment,
                        TaxPaymentDMO::benBankCode
                    ) {
                        it.benBankCode
                    }.orEmpty(),
                    benBankName = benBankName ?: getDefaultFieldFromPayments(
                        listPayment,
                        TaxPaymentDMO::benBankName
                    ) {
                        it.benBankName
                    }.orEmpty(),
                )

                val shouldFetchTreasuryDetail =
                    benBankCode.isNullOrEmpty() && state.treasurySelected.isNotNull()

                state to TaxPaymentInfoSideEffect.InitScreen(
                    state.treasurySelected,
                    shouldFetchTreasuryDetail
                )
            }

            is TaxPaymentInfoViewEvent.RetryFetchData -> {
                if (event.type == TaxPaymentApiCall.VALIDATE_CUSTOMS_DUTY) {
                    return previousState to TaxPaymentInfoSideEffect.RetryValidateServerSide
                }
                val sideEffect = when (event.type) {
                    TaxPaymentApiCall.LIST_DEBIT_ACCOUNT -> TaxPaymentInfoSideEffect.GetListDebitAccount(
                        false
                    )

                    TaxPaymentApiCall.TREASURY_CODE -> TaxPaymentInfoSideEffect.GetListTreasury
                    TaxPaymentApiCall.REV_AUTH_CODE -> previousState.treasurySelected?.let {
                        TaxPaymentInfoSideEffect.GetListRevenueAuthority(it)
                    }

                    TaxPaymentApiCall.REV_ACC_CODE -> TaxPaymentInfoSideEffect.GetListRevenueAccount
                    TaxPaymentApiCall.ADM_AREA_CODE -> TaxPaymentInfoSideEffect.GetListAdministrativeArea
                    TaxPaymentApiCall.BEN_BANK_NAME -> previousState.treasurySelected?.let {
                        TaxPaymentInfoSideEffect.GetTreasuryDetail(
                            previousState.treasurySelected
                        )
                    }

                    else -> {
                        /*Handle nothing*/
                        null
                    }
                }
                previousState to sideEffect
            }

            is TaxPaymentInfoViewEvent.UpdateDebitAccountList -> {
                val defaultSelected = event.debitAccountList
                    .find { it.default == true }
                    ?: event.debitAccountList.firstOrNull()

                previousState.copy(
                    showDebitAccountBottomSheet = event.shouldShowBottomSheet,
                    listDebitAccounts = event.debitAccountList,
                ) to defaultSelected?.let {
                    TaxPaymentInfoSideEffect.CheckAndSetDebitAccount(defaultSelected)
                }
            }

            is TaxPaymentInfoViewEvent.UpdateTreasuryList -> {
                previousState.copy(
                    listTreasury = event.treasuryList,
                    showTreasuryBottomSheet = true
                ) to null
            }

            is TaxPaymentInfoViewEvent.UpdateRevenueAuthorityList -> {
                previousState.copy(
                    listRevenueAuthority = event.revenueAuthorityList,
                    showRevenueAuthorityBottomSheet = true
                ) to null
            }

            is TaxPaymentInfoViewEvent.UpdateRevenueAccountList -> {
                previousState.copy(
                    listRevenueAccount = event.revenueAccountList,
                    showRevenueAccountBottomSheet = true
                ) to null
            }

            is TaxPaymentInfoViewEvent.UpdateAdministrativeAreaList -> {
                previousState.copy(
                    listAdministrativeArea = event.administrativeAreaList,
                    showAdministrativeAreaBottomSheet = true
                ) to null
            }

            is TaxPaymentInfoViewEvent.UpdateSelectedDebitAccount -> {
                previousState.copy(
                    debitAccountSelected = event.debitAccount,
                    showDebitAccountBottomSheet = false
                ) to null
            }

            is TaxPaymentInfoViewEvent.UpdateSelectedTreasury -> {
                previousState.copy(
                    treasurySelected = event.treasury,
                    administrativeAreaSelected = event.treasury?.let {
                        AdministrativeAreaDMO(it.admAreaCode, it.admAreaName)
                    },
                    showTreasuryBottomSheet = false,
                    listRevenueAuthority = null,
                    revenueAuthoritySelected = null,
                ) to event.treasury?.let { TaxPaymentInfoSideEffect.GetTreasuryDetail(it) }
            }

            is TaxPaymentInfoViewEvent.UpdateSelectedRevenueAuthority -> {
                previousState.copy(
                    revenueAuthoritySelected = event.revenueAuthority,
                    showRevenueAuthorityBottomSheet = false
                ) to null
            }

            is TaxPaymentInfoViewEvent.UpdateSelectedRevenueAccount -> {
                previousState.copy(
                    revenueAccountSelected = event.revenueAccount,
                    showRevenueAccountBottomSheet = false
                ) to null
            }

            is TaxPaymentInfoViewEvent.UpdateSelectedAdministrativeArea -> {
                previousState.copy(
                    administrativeAreaSelected = event.administrativeArea,
                    showAdministrativeAreaBottomSheet = false
                ) to null
            }

            is TaxPaymentInfoViewEvent.UpdateNoteToApproverString -> {
                previousState.copy(
                    noteToApprover = event.value
                ) to null
            }

            is TaxPaymentInfoViewEvent.UpdateSelectedTaxPayerEntity -> {
                previousState.copy(
                    showTaxPayerEntityBottomSheet = false,
                    taxPayerEntitySelected = event.taxPayerEntity
                ) to null
            }

            is TaxPaymentInfoViewEvent.UpdateBenBankInfo -> {
                previousState.copy(
                    benBankName = event.treasury.benBankName.orEmpty(),
                    benBankCode = event.treasury.benBankCode.orEmpty()
                ) to null
            }

            is TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility -> {
                when (event.field) {
                    TaxPaymentFormField.DEBIT_ACCOUNT -> {
                        if (previousState.listDebitAccounts == null && event.show) {
                            previousState to TaxPaymentInfoSideEffect.GetListDebitAccount(true)
                        } else {
                            previousState.copy(showDebitAccountBottomSheet = event.show) to null
                        }
                    }

                    TaxPaymentFormField.TREASURY_CODE -> {
                        if (previousState.listTreasury == null && event.show) {
                            previousState to TaxPaymentInfoSideEffect.GetListTreasury
                        } else {
                            previousState.copy(showTreasuryBottomSheet = event.show) to null
                        }
                    }

                    TaxPaymentFormField.REV_AUTH_CODE -> {
                        if (previousState.listRevenueAuthority == null && previousState.treasurySelected != null && event.show) {
                            previousState to TaxPaymentInfoSideEffect.GetListRevenueAuthority(
                                previousState.treasurySelected
                            )
                        } else {
                            previousState.copy(showRevenueAuthorityBottomSheet = event.show) to null
                        }
                    }

                    TaxPaymentFormField.REV_ACC_CODE -> {
                        if (previousState.listRevenueAccount == null && event.show) {
                            previousState to TaxPaymentInfoSideEffect.GetListRevenueAccount
                        } else {
                            previousState.copy(showRevenueAccountBottomSheet = event.show) to null
                        }
                    }

                    TaxPaymentFormField.ADM_AREA_CODE -> {
                        if (previousState.listAdministrativeArea == null && event.show) {
                            previousState to TaxPaymentInfoSideEffect.GetListAdministrativeArea
                        } else {
                            previousState.copy(showAdministrativeAreaBottomSheet = event.show) to null
                        }
                    }

                    TaxPaymentFormField.TAX_PAYER_ENTITY -> {
                        previousState.copy(showTaxPayerEntityBottomSheet = event.show) to null
                    }
                }
            }

            is TaxPaymentInfoViewEvent.OnCallApiError -> {
                val newState = if (event.type == TaxPaymentApiCall.DETAIL_DEBIT_ACCOUNT) {
                    previousState.copy(debitAccountSelected = null)
                } else {
                    previousState
                }

                newState to TaxPaymentInfoSideEffect.OnCallApiError(
                    event.errorCode,
                    event.errorMessage,
                    event.type,
                )
            }

            is TaxPaymentInfoViewEvent.DeletePayment -> {
                val shouldPopToPReducer = previousState.listPayment.size == 1
                previousState.copy(
                    listPayment = previousState.listPayment.filter { it.uniqueId != event.payment.uniqueId }
                ) to TaxPaymentInfoSideEffect.DeletePaymentSuccess(shouldPopToPReducer)
            }

            is TaxPaymentInfoViewEvent.ValidateServerSideSuccess -> {
                if (previousState.taxPayerInfo == null ||
                    previousState.debitAccountSelected == null ||
                    previousState.treasurySelected == null ||
                    previousState.revenueAuthoritySelected == null ||
                    previousState.revenueAccountSelected == null ||
                    previousState.administrativeAreaSelected == null
                ) {
                    return previousState to null
                }
                val newPaymentList = previousState.listPayment.map {
                    it.copy(
                        treasuryCode = previousState.treasurySelected.treasuryCode,
                        treasuryName = previousState.treasurySelected.treasuryName,
                        revAuthCode = previousState.revenueAuthoritySelected.revAuthCode,
                        revAuthName = previousState.revenueAuthoritySelected.revAuthName,
                        revAccCode = previousState.revenueAccountSelected.revAccCode,
                        revAccName = previousState.revenueAccountSelected.revAccName,
                        admAreaCode = previousState.administrativeAreaSelected.admAreaCode,
                        admAreaName = previousState.administrativeAreaSelected.admAreaName,
                    )
                }

                previousState to TaxPaymentInfoSideEffect.ValidateServerSideSuccess(
                    CustomsFeeInfoStep3Content(
                        taxPayerInfo = previousState.taxPayerInfo,
                        taxDelegatorInfo = previousState.taxDelegatorInfo,
                        listPayment = newPaymentList,
                        debitAccount = previousState.debitAccountSelected,
                        treasury = previousState.treasurySelected,
                        revenueAuthority = previousState.revenueAuthoritySelected,
                        revenueAccount = previousState.revenueAccountSelected,
                        administrativeArea = previousState.administrativeAreaSelected,
                        taxPayerEntity = previousState.taxPayerEntitySelected,
                        noteToApprover = previousState.noteToApprover,
                        validateResult = event.validateCustomsDutyDMO,
                        isEditingTransaction = previousState.editingTransactionId != null,
                        priorityTransaction = previousState.priorityTransaction,
                        customerTransactionCode = previousState.customerTransactionCode,
                        benBankCode = previousState.benBankCode,
                        benBankName = previousState.benBankName,
                    )
                )
            }

            is TaxPaymentInfoViewEvent.ValidateBeforeContinue -> {
                when {
                    hasMissingRequiredFields(previousState) -> {
                        return previousState.copy(
                            formError = TaxPaymentFormFieldValidateError.RequiredFieldNotFilled
                        ) to null
                    }

                    else -> {
                        findFaultPayment(previousState.listPayment)?.let { faultPayment ->
                            return previousState.copy(
                                formError = TaxPaymentFormFieldValidateError.PaymentsFieldIsNotCompleted(
                                    faultPayment
                                )
                            ) to TaxPaymentInfoSideEffect.ValidateBeforeContinueFailed(
                                TaxPaymentValidateFailedPopupType.PAYMENTS_NOT_FILLED
                            )
                        }
                    }
                }

                return previousState to TaxPaymentInfoSideEffect.ValidateClientSideSuccess
            }

            is TaxPaymentInfoViewEvent.ClearData -> {
                previousState.clearData() to null
            }

            is TaxPaymentInfoViewEvent.UpdatePaymentItem -> {
                val data = event.data
                val updateData = previousState.listPayment.map {
                    if (it.uniqueId == data.uniqueId) {
                        it.copy(
                            declarationNo = data.declarationNo,
                            declarationDate = data.declarationDate,
                            chapterCode = data.chapterCode,
                            chapterName = data.chapterName ?: "--",
                            ecName = data.ecName ?: "--",
                            ecCode = data.ecCode,
                            ccy = data.ccy,
                            amount = data.amount,
                            transDesc = data.transDesc,
                            taxTypeName = data.taxTypeName ?: "--",
                            taxTypeCode = data.taxTypeCode,
                            ccName = data.ccName ?: "--",
                            ccCode = data.ccCode,
                            eiTypeName = data.eiTypeName ?: "--",
                            eiTypeCode = data.eiTypeCode,
                        )
                    } else {
                        it
                    }
                }
                previousState.copy(listPayment = updateData) to null
            }

            is TaxPaymentInfoViewEvent.TogglePriorityTransaction -> {
                previousState.copy(priorityTransaction = event.priorityTransaction) to null
            }

            is TaxPaymentInfoViewEvent.UpdateCustomerTransactionCode -> {
                previousState.copy(customerTransactionCode = event.value) to null
            }
        }
    }

    private fun hasMissingRequiredFields(state: TaxPaymentInfoUiState): Boolean {
        return listOf(
            state.treasurySelected,
            state.revenueAuthoritySelected,
            state.revenueAccountSelected,
            state.administrativeAreaSelected,
            state.debitAccountSelected,
        ).any { it == null }
    }

    private fun findFaultPayment(payments: List<TaxPaymentDMO>): TaxPaymentDMO? {
        return payments.find {
            it.declarationNo.isEmpty() ||
                    it.declarationDate.isEmpty() ||
                    it.chapterCode.isEmpty() ||
                    it.ecCode.isEmpty() ||
                    it.amount.isEmpty() ||
                    it.ccy.isEmpty() ||
                    it.transDesc.isEmpty() ||
                    it.taxTypeCode.isEmpty() ||
                    it.ccCode.isEmpty() ||
                    it.eiTypeCode.isEmpty()
        }
    }

    private fun <T> getDefaultFieldFromPayments(
        listPayment: List<TaxPaymentDMO>,
        field: KProperty1<TaxPaymentDMO, String?>,
        builder: (TaxPaymentDMO) -> T,
    ): T? {
        return listPayment.find { field.get(it) != null }?.let(builder)
    }
}