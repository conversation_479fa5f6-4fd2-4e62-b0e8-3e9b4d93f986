package vn.com.bidv.feature.government.service.util

import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants

object GovernmentServiceUtils {
    fun isPaymentDuplicated(payment1: TaxPaymentDMO, payment2: TaxPaymentDMO): Boolean {
        fun compareNullableField(field1: String?, field2: String?): Boolean {
            if (field1 == null || field2 == null) return true
            return field1 == field2
        }
        return payment1.declarationNo == payment2.declarationNo &&
                payment1.declarationDate == payment2.declarationDate &&
                payment1.ecCode == payment2.ecCode &&
                payment1.chapterCode == payment2.chapterCode &&
                payment1.amount == payment2.amount &&
                payment1.ccy == payment2.ccy &&
                payment1.taxTypeCode == payment2.taxTypeCode &&
                payment1.eiTypeCode == payment2.eiTypeCode &&
                payment1.ccCode == payment2.ccCode &&
                payment1.transDesc == payment2.transDesc &&
                compareNullableField(payment1.treasuryCode, payment2.treasuryCode) &&
                compareNullableField(payment1.revAccCode, payment2.revAccCode) &&
                compareNullableField(payment1.revAuthCode, payment2.revAuthCode) &&
                compareNullableField(payment1.admAreaCode, payment2.admAreaCode)
    }

    fun getCurrencyMaxLength(currencies: List<String>?): Int {
        return when (currencies?.firstOrNull()) {
            SdkBaseConstants.MoneyCurrencyConstants.VND,
            SdkBaseConstants.MoneyCurrencyConstants.CLP,
            SdkBaseConstants.MoneyCurrencyConstants.JPY,
            SdkBaseConstants.MoneyCurrencyConstants.KRW
                -> Constants.MAX_LENGTH_TEXT_INPUT_15

            null -> Constants.MAX_LENGTH_TEXT_INPUT_13
            else -> Constants.MAX_LENGTH_TEXT_INPUT_13
        }
    }

    fun formatCodeName(code: String?, name: String?, emptyString: String = ""): String {
        return when {
            code.isNullOrEmpty() && name.isNullOrEmpty() -> emptyString
            code.isNullOrEmpty() -> name.orEmpty()
            name.isNullOrEmpty() -> code
            else -> "$code - $name"
        }
    }
}