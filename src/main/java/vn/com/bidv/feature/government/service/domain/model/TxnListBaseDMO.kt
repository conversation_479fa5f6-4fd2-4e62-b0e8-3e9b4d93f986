package vn.com.bidv.feature.government.service.domain.model

interface TxnListBaseDMO {
    val txnId: String?
    val debitAccNo: String?
    val taxCode: String?
    val declarationNo: String?
    val amount: String?
    val ccy: String?
    val batchNo: String?
    val status: String?
    val createdDate: String?
    val treasuryCode: String?
    val treasuryName: String?
    val admAreaCode: String?
    val admAreaName: String?
    val revAccCode: String?
    val revAccName: String?
    val revAuthCode: String?
    val revAuthName: String?
    val statusName: String?
} 