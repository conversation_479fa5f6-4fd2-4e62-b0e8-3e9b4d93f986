package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step3

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.google.gson.Gson
import vn.com.bidv.common.utils.unpackV2
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.IBankSectionHeader
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.LeadingType
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.model.TransactionApprovalContent
import vn.com.bidv.feature.government.service.model.CreateTransactionType
import vn.com.bidv.feature.government.service.model.CustomsFeeInfoStep3Content
import vn.com.bidv.feature.government.service.domain.model.TxnInitPushDMO
import vn.com.bidv.feature.government.service.model.TxnInitPushType
import vn.com.bidv.feature.government.service.model.UserType
import vn.com.bidv.feature.government.service.model.toTransactionDetailDMO
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute
import vn.com.bidv.feature.government.service.ui.common.ItemCardBody
import vn.com.bidv.feature.government.service.ui.common.StepProgressBar
import vn.com.bidv.feature.government.service.ui.common.TaxPaymentCardContent
import vn.com.bidv.feature.government.service.ui.common.views.AmountSection
import vn.com.bidv.feature.government.service.ui.common.views.BankCardSection
import vn.com.bidv.feature.government.service.ui.common.views.TransactionDetailCard
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.CustomsFeeInfoMainReducer
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.CustomsFeeInfoMainViewModel
import vn.com.bidv.feature.government.service.util.FunctionRule
import vn.com.bidv.feature.government.service.util.takeIfRuleMatches
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.data.ReloadFunctionKey
import vn.com.bidv.sdkbase.data.ReloadKey
import vn.com.bidv.sdkbase.data.ReloadModuleKey
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun CustomsFeeInfoStep3Screen(
    navController: NavHostController
) {

    val createFlowViewModel: CustomsFeeInfoMainViewModel = hiltViewModel()
    val (createFlowUiState, createFlowOnEvent, _) = createFlowViewModel.unpackV2()

    val viewModel: CustomsFeeInfoStep3ViewModel = hiltViewModel()
    val style = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    val step3Content = createFlowUiState.value.step3Content ?: CustomsFeeInfoStep3Content.empty()

    val availableBalance = step3Content.debitAccount.availableBalance?.toLongOrNull() ?: 0L
    val txnAmount = step3Content.validateResult.amount?.toLongOrNull() ?: 0L
    val totalFees = step3Content.validateResult.feeTotal?.toLongOrNull() ?: 0L
    val minBalance = step3Content.debitAccount.minBalance?.toLongOrNull() ?: 0L

    val remainingBalance = availableBalance - txnAmount - totalFees

    val confirmMessage = when {
        remainingBalance < 0 -> {
            stringResource(R.string.tai_khoan_khong_du_so_du_quy_khach_co_muon_tiep_tuc)
        }

        remainingBalance in 0..<minBalance -> {
            stringResource(R.string.tai_khoan_khong_du_so_du_toi_thieu_quy_khach_co_muon_tiep_tuc)
        }

        else -> {
            null
        }
    }

    val modalConfirmType = when {
        confirmMessage.isNullOrEmpty()-> ModalConfirmType.Question
        else -> ModalConfirmType.Warning
    }

    val confirmTitle = when {
        confirmMessage.isNullOrEmpty() -> stringResource(R.string.xac_nhan_day_duyet)
        else -> stringResource(R.string.canh_bao)
    }

    val positiveButtonText = when {
        confirmMessage.isNullOrEmpty() -> stringResource(R.string.day_duyet)
        else -> stringResource(R.string.tiep_tuc)
    }

    val negativeButtonText = when {
        confirmMessage.isNullOrEmpty() -> stringResource(R.string.huy)
        else -> stringResource(R.string.quay_lai)
    }

    BaseScreen(
        navController = navController,
        viewModel = viewModel,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(R.string.xac_nhan),
            onNavigationClick = {
                createFlowOnEvent(
                    CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.PopToStepTwo
                )
            }
        ),
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is CustomsFeeInfoStep3Reducer.CustomsFeeInfoStep3ViewEffect.NavigateBackToListPendingTrans -> {
                    viewModel.requestReloadData(
                        ReloadKey(
                            ReloadModuleKey.GOVERNMENT_SERVICE,
                            ReloadFunctionKey.LIST_PENDING
                        )
                    )
                    navController.popBackStack()
                }

                else -> {
                    /*Handle nothing*/
                }
            }
        }
    ) { viewState, onEvent ->
        if (!viewState.isInitialized) {
            onEvent(
                CustomsFeeInfoStep3Reducer.CustomsFeeInfoStep3ViewEvent.InitScreen(
                    step3Content.isEditingTransaction
                )
            )
        }
        Column {
            StepProgressBar(Constants.FLOW_CREATE_STEP_THREE)
            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
                    .padding(IBSpacing.spacingM),
                verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM)
            ) {
                BankCardSection(step3Content.toTransactionDetailDMO(LocalContext.current))
                AmountSection(
                    step3Content.validateResult.amount.formatMoney(
                        step3Content.validateResult.feeCcy,
                        isShowCurrCode = true
                    ),
                    step3Content.validateResult.amountText ?: "--"
                )
                TransactionDetailCard(
                    step3Content.toTransactionDetailDMO(LocalContext.current),
                    showOrgId = true,
                    showNoteToAuthorizer = true,
                )
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(IBSpacing.spacingM))
                        .background(colorScheme.bgMainTertiary)
                        .padding(
                            start = IBSpacing.spacingM,
                            end = IBSpacing.spacingM,
                            bottom = IBSpacing.spacingS
                        )
                ) {
                    IBankSectionHeader(
                        modifier = Modifier.offset(x = -IBSpacing.spacingM),
                        shLeadingType = LeadingType.Dash(),
                        shSectionTitle = stringResource(R.string.thong_tin_khoan_nop),
                    )
                    step3Content.listPayment.forEach { item ->
                        TaxPaymentCardContent(
                            modifier = Modifier
                                .border(
                                    1.dp,
                                    color = colorScheme.borderMainSecondary,
                                    shape = RoundedCornerShape(size = IBSpacing.spacingXs)
                                )
                                .background(
                                    color = colorScheme.bgMainTertiary,
                                    shape = RoundedCornerShape(size = IBSpacing.spacingXs)
                                ),
                            isChecked = false,
                            cardHeader = {
                                Text(
                                    modifier = Modifier
                                        .weight(1f, fill = false),
                                    text = item.getHeaderString(),
                                    style = style.titleTitle_s,
                                    color = colorScheme.contentMainPrimary,
                                    overflow = TextOverflow.Clip
                                )
                            },
                            cardContent = {
                                ItemCardBody(
                                    modifier = Modifier.padding(top = IBSpacing.spacingXs),
                                    item = item
                                )
                            },
                            onClick = {
                                navController.navigate(
                                    GovernmentServiceRoute.TaxPaymentDetailRoute(args = item)
                                )
                            },
                        )
                    }
                }

            }
            IBankActionBar(
                isVertical = false,
                buttonNegative = takeIfRuleMatches(rule = FunctionRule.Save) {
                    DialogButtonInfo(stringResource(R.string.luu)) {
                        onEvent(
                            CustomsFeeInfoStep3Reducer.CustomsFeeInfoStep3ViewEvent.SaveTransaction(
                                step3Content.validateResult.transKey ?: "--"
                            )
                        )
                    }
                },
                buttonPositive = takeIfRuleMatches(FunctionRule.PushApproval) {
                    DialogButtonInfo(stringResource(R.string.day_duyet)) {
                        val initPushType =
                            if (createFlowUiState.value.createTransactionType is CreateTransactionType.EditTransaction) {
                                TxnInitPushType.PUSH_EDIT
                            } else {
                                TxnInitPushType.PUSH_SAVE
                            }
                        val initPushDMO = TxnInitPushDMO(
                            type = initPushType,
                            transKey = step3Content.validateResult.transKey,
                        )
                        navController.navigate(
                            GovernmentServiceRoute.BaseTransactionApprovalRoute(
                                TransactionApprovalContent(
                                    isShowPopupConfirm = true,
                                    dataString = Gson().toJson(initPushDMO),
                                    userType = UserType.MAKER,
                                    type = initPushType,
                                    modalConfirmType = modalConfirmType,
                                    confirmTitle = confirmTitle,
                                    confirmMessage = confirmMessage,
                                    positiveButtonText = positiveButtonText,
                                    negativeButtonText = negativeButtonText,
                                )
                            )
                        )
                    }
                },
            )
        }
    }
}

