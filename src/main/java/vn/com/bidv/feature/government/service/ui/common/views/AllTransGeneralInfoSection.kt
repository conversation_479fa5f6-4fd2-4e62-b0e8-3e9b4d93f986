package vn.com.bidv.feature.government.service.ui.common.views

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.TransactionStatusBase

@Composable
fun AllTransGeneralInfoSection(
    transactionData: TransactionDetailDMO
) {
    InfoCard(title = stringResource(R.string.thong_tin_chung)) {

        val listDetailBadge = listOf(
            R.string.trang_thai_giao_dich to Pair(
                transactionData.status,
                transactionData.statusName
            ),
            R.string.trang_thai_hach_toan to Pair(
                transactionData.accountingStatus,
                transactionData.accountingStatusName
            ),
            R.string.trang_thai_ket_noi_voi_hai_quan to Pair(
                transactionData.customsConnStatus,
                transactionData.customsConnStatusName
            )
        )

        val listDetail = listOf(
            R.string.so_tham_chieu_tcc to transactionData.tccRefNo,
            R.string.nguoi_tao to transactionData.createdBy,
            R.string.nguoi_duyet to transactionData.approvedBy,
            R.string.ma_giao_dich_khach_hang to transactionData.orgId,
            R.string.ma_giao_dich to transactionData.transactionId,
            R.string.ghi_chu_den_nguoi_duyet to transactionData.noteToAuthorizer,
            R.string.ly_do_tu_choi to transactionData.denialReasonNote.takeIf {
                transactionData.status == TransactionStatusBase.REJECTED.statusCode
            },
            R.string.so_tham_chieu_lo to transactionData.batchNumber,
        )

        listDetailBadge.forEach { (label, value) ->
            val status = transactionData.status?.let { TransactionStatusBase.fromString(it) }
            if (status != null && value.second.isNotNullOrEmpty()) {
                DetailItemWithBadge(
                    label = stringResource(label),
                    badgeTitle = value.second.orEmpty(),
                    badgeColor = status.color
                )
            } else {
                DetailItem(
                    label = stringResource(label),
                    value = "--"
                )
            }
        }

        listDetail.forEach { (label, value) ->
            if (value.isNotNullOrEmpty()) {
                DetailItem(
                    label = stringResource(label),
                    value = value.orEmpty()
                )
            }
        }
    }
}

@Preview
@Composable

fun PreviewAllTransGeneralInfoSection() {
    val data = TransactionDetailDMO(transactionId = "243")
    AllTransGeneralInfoSection(data)
}
