package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO
import vn.com.bidv.feature.government.service.model.FieldStatus
import vn.com.bidv.feature.government.service.model.TaxPaymentFormField
import vn.com.bidv.feature.government.service.model.TaxPaymentFormFieldValidateError
import vn.com.bidv.feature.government.service.ui.common.CommonDropDown
import vn.com.bidv.feature.government.service.ui.common.CommonSearchDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.TaxPaymentInfoUiState
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.TaxPaymentInfoViewEvent
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
internal fun TreasuryDropdown(uiState: TaxPaymentInfoUiState, onEvent: (TaxPaymentInfoViewEvent) -> Unit,) {
    CommonDropDown(
        required = true,
        showClearButton = false,
        labelText = stringResource(R.string.ma_va_ten_kho_bac),
        selectedItem = uiState.treasurySelected,
        displayTextSelector = { formatCodeName(it?.treasuryCode, it?.treasuryName) },
        fieldError = if (uiState.formError == TaxPaymentFormFieldValidateError.RequiredFieldNotFilled && uiState.treasurySelected == null) {
            FieldStatus.INVALID
        } else null,
        onClickClear = {
            onEvent(TaxPaymentInfoViewEvent.UpdateSelectedTreasury(null))
        },
        onClickEnd = {
            onEvent(
                TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
                    TaxPaymentFormField.TREASURY_CODE,
                    true
                )
            )
        }
    )
}

@Composable
internal fun TreasurySearchDialog(
    listData: List<TreasuryDMO>,
    itemSelected: TreasuryDMO?,
    onDismiss: (TreasuryDMO?) -> Unit,
) {
    CommonSearchDialog(
        title = stringResource(R.string.ma_va_ten_kho_bac),
        itemSelected = itemSelected,
        listData = listData,
        showSearchBox = true,
        searchFilter = { item, query ->
            val search = "${item.treasuryCode}${item.treasuryName}"
            VNCharacterUtil.removeAccent(search)
                .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
        },
        onDismiss = onDismiss,
        itemTitleSelector = { formatCodeName(it.treasuryCode, it.treasuryName) }
    )
}