package vn.com.bidv.feature.government.service.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

/**
 * Model for transaction details
 * @param transactionId
 * @param debitAccNumber Số tài khoản nộp thuế
 * @param debitAccName Tên tài khoản nộp thuế
 * @param payerName Tên người nộp thuế
 * @param payerAddress Địa chỉ người nộp thuế
 * @param payerType Loại hình người nộp thuế
 * @param payerTaxCode Mã số thuế người nộp thuế
 * @param altTaxCode Mã số thuế người nộp thuế thay
 * @param altPayerName Tên người nộp thuế thay
 * @param altPayerAddress Địa chỉ người nộp thuế thay
 * @param paymentAmount Số tiền nộp thuế
 * @param paymentAmountText Số tiền nộp thuế dạng văn bản
 * @param ccy Mã đơn vị iền tệ
 * @param batchNumber Số lô (chỉ áp dụng cho giao dịch khởi tạo theo lô)
 * @param status Trạng thái giao dịch
 * @param createdDate Ngày tạo giao dịch
 * @param accountingStatus Mã trạng thái hạch toán
 * @param accountingStatusName Tên trạng thái hạch toán
 * @param customsConnStatus Mã trạng thái kết nối với Hải quan
 * @param customsConnStatusName Tên trạng thái kết nối với Hải quan
 * @param createdBy Người tạo giao dịch
 * @param approvedBy Người duyệt giao dịch
 * @param tccRefNo Mã tham chiếu TCC
 * @param treasuryCode Mã kho bạc
 * @param treasuryName Tên kho bạc
 * @param admAreaCode Mã địa bàn hành chính
 * @param admAreaName Tên địa bàn hành chính
 * @param revAccCode Mã tài khoản thu ngân sách nhà nước
 * @param revAccName Tên tài khoản thu ngân sách nhà nước
 * @param authorityCode Mã cơ quan thu
 * @param authorityName Tên cơ quan thu
 * @param benBankCode Mã ngân hàng thụ hưởng (Beneficiary Bank Code)
 * @param benBankName Tên ngân hàng thụ hưởng (Beneficiary Bank Name)
 * @param feeTotal Tổng phí
 * @param feeMethod Phí áp dụng
 * @param noteToAuthorizer Ghi chú cho người duyệt
 * @param denialReasonNote Ghi chú lý do từ chối
 * @param taxItems Danh sách các khoản nộp
 * @param statusName Tên trạng thái giao dịch
 * @param priority Giao dịch ưu tiên
 * @param orgId Mã giao dịch khách hàng
 */

@Serializable
data class TransactionDetailDMO(

    @SerializedName("txnId")
    val transactionId: String,

    @SerializedName("debitAccNo")
    val debitAccNumber: String? = null,

    @SerializedName("debitAccName")
    val debitAccName: String? = null,

    @SerializedName("taxCode")
    val payerTaxCode: String? = null,

    @SerializedName("altTaxCode")
    val altTaxCode: String? = null,

    @SerializedName("payerName")
    val payerName: String? = null,

    @SerializedName("altPayerName")
    val altPayerName: String? = null,

    @SerializedName("payerAddr")
    val payerAddress: String? = null,

    @SerializedName("altPayerAddr")
    val altPayerAddress: String? = null,

    @SerializedName("payerType")
    val payerType: Int? = null,

    @SerializedName("payerTypeName")
    val payerTypeName: String? = null,

    @SerializedName("amount")
    val paymentAmount: String? = null,

    @SerializedName("ccy")
    val ccy: String? = null,

    @SerializedName("batchNo")
    val batchNumber: String? = null,

    @SerializedName("status")
    val status: String? = null,

    @SerializedName("createdDate")
    val createdDate: String? = null,

    @SerializedName("accountingStatus")
    val accountingStatus: String? = null,

    @SerializedName("accountingStatusName")
    val accountingStatusName: String? = null,

    @SerializedName("customsConnStatus")
    val customsConnStatus: String? = null,

    @SerializedName("customsConnStatusName")
    val customsConnStatusName: String? = null,

    @SerializedName("createdBy")
    val createdBy: String? = null,

    @SerializedName("approvedBy")
    val approvedBy: String? = null,

    @SerializedName("tccRefNo")
    val tccRefNo: String? = null,

    @SerializedName("treasuryCode")
    val treasuryCode: String? = null,

    @SerializedName("treasuryName")
    val treasuryName: String? = null,

    @SerializedName("admAreaCode")
    val admAreaCode: String? = null,

    @SerializedName("admAreaName")
    val admAreaName: String? = null,

    @SerializedName("revAccCode")
    val revAccCode: String? = null,

    @SerializedName("revAccName")
    val revAccName: String? = null,

    @SerializedName("revAuthCode")
    val authorityCode: String? = null,

    @SerializedName("revAuthName")
    val authorityName: String? = null,

    @SerializedName("feeTotal")
    val feeTotal: String? = null,

    @SerializedName("feeCcy")
    val feeCcy: String? = null,

    @SerializedName("feeOpt")
    val feeMethod: String? = null,

    @SerializedName("raNote")
    val noteToAuthorizer: String? = null,

    @SerializedName("approvalNote")
    val denialReasonNote: String? = null,

    @SerializedName("taxItems")
    val taxItems: List<TaxPaymentDMO>? = null,

    @SerializedName("statusName")
    val statusName: String? = null,

    @SerializedName("amountText")
    val paymentAmountText: String? = null,

    @SerializedName("priority")
    val priority: Boolean? = null,

    @SerializedName("orgId")
    val orgId: String? = null,

    /* Ngân hàng thụ hưởng */
    @SerializedName("benBankCode")
    val benBankCode: String? = null,

    /* Tên ngân hàng thụ hưởng */
    @SerializedName("benBankName")
    val benBankName: String? = null
)