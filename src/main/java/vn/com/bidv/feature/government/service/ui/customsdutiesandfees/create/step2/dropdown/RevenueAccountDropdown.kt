package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.IBankContextMenu
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState
import vn.com.bidv.feature.government.service.domain.model.RevenueAccountDMO
import vn.com.bidv.feature.government.service.model.FieldStatus
import vn.com.bidv.feature.government.service.model.TaxPaymentFormField
import vn.com.bidv.feature.government.service.model.TaxPaymentFormFieldValidateError
import vn.com.bidv.feature.government.service.ui.common.CommonDropDown
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.TaxPaymentInfoUiState
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.TaxPaymentInfoViewEvent
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
internal fun RevenueAccountDropdown(
    uiState: TaxPaymentInfoUiState,
    onEvent: (TaxPaymentInfoViewEvent) -> Unit,
) {
    CommonDropDown(
        required = true,
        showClearButton = false,
        labelText = stringResource(R.string.tai_khoan_thu_nsnn),
        selectedItem = uiState.revenueAccountSelected,
        displayTextSelector = { formatCodeName(it?.revAccCode, it?.revAccName) },
        fieldError = if (uiState.formError == TaxPaymentFormFieldValidateError.RequiredFieldNotFilled && uiState.revenueAccountSelected == null) {
            FieldStatus.INVALID
        } else null,
        onClickClear = {
            onEvent(TaxPaymentInfoViewEvent.UpdateSelectedRevenueAccount(null))
        },
        onClickEnd = {
            onEvent(
                TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
                    TaxPaymentFormField.REV_ACC_CODE,
                    true
                )
            )
        }
    )
}

@Composable
internal fun RevenueAccountSearchDialog(
    listData: List<RevenueAccountDMO>,
    itemSelected: RevenueAccountDMO?,
    onDismiss: (RevenueAccountDMO?) -> Unit,
) {
    IBankSearchDialog(
        title = stringResource(R.string.tai_khoan_thu_nsnn),
        itemSelected = itemSelected,
        compareKey = { it.revAccCode },
        showSearchBox = true,
        listData = listData,
        searchFilter = { item, key ->
            val search = "${item.revAccCode}${item.revAccName}"
            VNCharacterUtil.removeAccent(search)
                .contains(VNCharacterUtil.removeAccent(key), ignoreCase = true)
        },
        state = SearchDialogState.CONTENT,
        onRequestDismiss = onDismiss,
        listSearchFilterText = listOf(),
    ) { _, item ->
        IBankContextMenu(
            leadingIconResId = vn.com.bidv.designsystem.R.drawable.the,
            isSelected = item.isSelected,
            title = item.data.revAccCode.orEmpty(),
            description = item.data.revAccName.orEmpty(),
        )
    }
}