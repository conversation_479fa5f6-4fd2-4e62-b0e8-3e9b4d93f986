package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.government.service.domain.model.AdministrativeAreaDMO
import vn.com.bidv.feature.government.service.domain.model.BalanceAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAuthorityDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.feature.government.service.domain.model.TransactionTemplateDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO
import vn.com.bidv.feature.government.service.model.CreateTransactionType
import vn.com.bidv.feature.government.service.model.CustomsFeeInfoStep1Content
import vn.com.bidv.feature.government.service.model.CustomsFeeInfoStep2Content
import vn.com.bidv.feature.government.service.model.CustomsFeeInfoStep3Content
import vn.com.bidv.feature.government.service.model.GetTransactionDetailPurpose
import vn.com.bidv.feature.government.service.model.TaxPayerEntity
import vn.com.bidv.feature.government.service.model.TaxPayerInfo
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO

class CustomsFeeInfoMainReducer :
    Reducer<CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState, CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent, CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEffect> {

    @Immutable
    data class CustomsFeeInfoMainViewState(
        val currentStep: Int = 0,
        private val step2Content: CustomsFeeInfoStep2Content? = null,
        val step3Content: CustomsFeeInfoStep3Content? = null,
        val createTransactionType: CreateTransactionType = CreateTransactionType.CreateNewTransaction,
    ) : ViewState {
        fun getStep1Content(): CustomsFeeInfoStep1Content? {
            return when (createTransactionType) {
                is CreateTransactionType.CreateNewTransaction -> null

                is CreateTransactionType.CreateFromTemplate -> {
                    val templateTransDMO = createTransactionType.transactionTemplateDMO
                    val delegateMode = templateTransDMO.altTaxCode?.isNotEmpty() ?: false
                    CustomsFeeInfoStep1Content(
                        modeDelegate = delegateMode,
                        taxPayerInfo =
                            if (delegateMode && templateTransDMO.taxCode != null) {
                                TaxPayerInfo(
                                    templateTransDMO.taxCode,
                                    templateTransDMO.payerName.orEmpty(),
                                    templateTransDMO.payerAddr.orEmpty()
                                )
                            } else {
                                null
                            },
                        taxDelegatorInfo = null,
                        defaultTaxPayerInfo = null,
                        fetchedTaxId = templateTransDMO.taxItems ?: listOf(),
                        shouldFetchTaxPayerInfo = true,
                    )
                }

                is CreateTransactionType.CopyTransaction -> {
                    val transDetailDMO = createTransactionType.transactionDetailDMO
                    val delegateMode = transDetailDMO.altTaxCode?.isNotEmpty() ?: false
                    CustomsFeeInfoStep1Content(
                        modeDelegate = delegateMode,
                        taxPayerInfo =
                            if (delegateMode && transDetailDMO.payerTaxCode != null) {
                                TaxPayerInfo(
                                    transDetailDMO.payerTaxCode,
                                    transDetailDMO.payerName.orEmpty(),
                                    transDetailDMO.payerAddress.orEmpty()
                                )
                            } else {
                                null
                            },
                        taxDelegatorInfo = null,
                        defaultTaxPayerInfo = null,
                        fetchedTaxId = transDetailDMO.taxItems ?: listOf(),
                        shouldFetchTaxPayerInfo = true
                    )
                }

                is CreateTransactionType.EditTransaction -> {
                    val transDetailDMO = createTransactionType.pendingListDMO
                    val delegateMode = transDetailDMO.altTaxCode != null

                    CustomsFeeInfoStep1Content(
                        modeDelegate = delegateMode,
                        defaultTaxPayerInfo = if (delegateMode) {
                            TaxPayerInfo(
                                transDetailDMO.altTaxCode.orEmpty(),
                                transDetailDMO.altPayerName.orEmpty(),
                                transDetailDMO.altPayerAddress.orEmpty()
                            )
                        } else {
                            TaxPayerInfo(
                                transDetailDMO.payerTaxCode.orEmpty(),
                                transDetailDMO.payerName.orEmpty(),
                                transDetailDMO.payerAddress.orEmpty()
                            )
                        },
                        taxPayerInfo =
                            TaxPayerInfo(
                                transDetailDMO.payerTaxCode.orEmpty(),
                                transDetailDMO.payerName.orEmpty(),
                                transDetailDMO.payerAddress.orEmpty()
                            ),
                        taxDelegatorInfo =
                            transDetailDMO.altTaxCode?.let {
                                TaxPayerInfo(
                                    transDetailDMO.altTaxCode,
                                    transDetailDMO.altPayerName.orEmpty(),
                                    transDetailDMO.altPayerAddress.orEmpty()
                                )
                            },
                        fetchedTaxId = transDetailDMO.taxItems ?: listOf(),
                        shouldFetchTaxPayerInfo = false,
                        shouldShowDelegatePicker = false,
                    )
                }
            }
        }
        fun getStep2Content(): CustomsFeeInfoStep2Content {
            return when {
                step2Content == null -> {
                    CustomsFeeInfoStep2Content(
                        listPayment = listOf(),
                        taxPayerInfo = TaxPayerInfo("", "", ""),
                        taxDelegatorInfo = TaxPayerInfo("", "", ""),
                        taxPayerEntitySelected = TaxPayerEntity.INDIVIDUAL,
                        noteToApprover = "",
                        priorityTransaction = false,
                        customerTransactionCode = "",
                    )
                }

                createTransactionType is CreateTransactionType.CreateNewTransaction -> step2Content

                createTransactionType is CreateTransactionType.CreateFromTemplate -> {
                    val templateTransDMO = createTransactionType.transactionTemplateDMO

                    CustomsFeeInfoStep2Content(
                        listPayment = step2Content.listPayment,
                        taxPayerInfo = step2Content.taxPayerInfo,
                        taxDelegatorInfo = step2Content.taxDelegatorInfo,
                        treasurySelected = templateTransDMO.treasuryCode?.let { TreasuryDMO(it, templateTransDMO.treasuryName) },
                        revenueAuthoritySelected = templateTransDMO.revAuthCode?.let { RevenueAuthorityDMO(it, templateTransDMO.revAuthName) },
                        revenueAccountSelected = templateTransDMO.revAccCode?.let { RevenueAccountDMO(it, templateTransDMO.revAccName) },
                        administrativeAreaSelected = templateTransDMO.admAreaCode?.let { AdministrativeAreaDMO(it, templateTransDMO.admAreaName) },
                        taxPayerEntitySelected = TaxPayerEntity.fromInt(templateTransDMO.payerType) ?: TaxPayerEntity.BUSINESS,
                        noteToApprover = null,
                        priorityTransaction = null,
                        customerTransactionCode = null,
                        benBankCode = templateTransDMO.benBankCode.orEmpty(),
                        benBankName = templateTransDMO.benBankName.orEmpty()
                    )
                }

                createTransactionType is CreateTransactionType.CopyTransaction -> {
                    val transDetailDMO = createTransactionType.transactionDetailDMO

                    CustomsFeeInfoStep2Content(
                        listPayment = step2Content.listPayment,
                        taxPayerInfo = step2Content.taxPayerInfo,
                        taxDelegatorInfo = step2Content.taxDelegatorInfo,
                        treasurySelected = transDetailDMO.treasuryCode?.let { TreasuryDMO(it, transDetailDMO.treasuryName) },
                        revenueAuthoritySelected = transDetailDMO.authorityCode?.let { RevenueAuthorityDMO(it, transDetailDMO.authorityName) },
                        revenueAccountSelected = transDetailDMO.revAccCode?.let { RevenueAccountDMO(it, transDetailDMO.revAccName) },
                        administrativeAreaSelected = transDetailDMO.admAreaCode?.let { AdministrativeAreaDMO(it, transDetailDMO.admAreaName) },
                        taxPayerEntitySelected = TaxPayerEntity.fromInt(transDetailDMO.payerType) ?: TaxPayerEntity.BUSINESS,
                        noteToApprover = transDetailDMO.noteToAuthorizer,
                        priorityTransaction = null,
                        customerTransactionCode = null,
                        benBankCode = transDetailDMO.benBankCode.orEmpty(),
                        benBankName = transDetailDMO.benBankName.orEmpty()
                    )
                }

                createTransactionType is CreateTransactionType.EditTransaction -> {
                    val transDetailDMO = createTransactionType.pendingListDMO

                    CustomsFeeInfoStep2Content(
                        listPayment = step2Content.listPayment,
                        taxPayerInfo = step2Content.taxPayerInfo,
                        taxDelegatorInfo = step2Content.taxDelegatorInfo,
                        debitAccountSelected = transDetailDMO.debitAccNumber?.let { BalanceAccountDMO(it, transDetailDMO.debitAccName) },
                        treasurySelected = transDetailDMO.treasuryCode?.let { TreasuryDMO(it, transDetailDMO.treasuryName) },
                        revenueAuthoritySelected = transDetailDMO.authorityCode?.let { RevenueAuthorityDMO(it, transDetailDMO.authorityName) },
                        revenueAccountSelected = transDetailDMO.revAccCode?.let { RevenueAccountDMO(it, transDetailDMO.revAccName) },
                        administrativeAreaSelected = transDetailDMO.admAreaCode?.let { AdministrativeAreaDMO(it, transDetailDMO.admAreaName) },
                        taxPayerEntitySelected = TaxPayerEntity.fromInt(transDetailDMO.payerType) ?: TaxPayerEntity.BUSINESS,
                        noteToApprover = transDetailDMO.noteToAuthorizer,
                        priorityTransaction = null,
                        customerTransactionCode = null,
                        editingTransactionId = transDetailDMO.transactionId,
                        benBankCode = transDetailDMO.benBankCode.orEmpty(),
                        benBankName = transDetailDMO.benBankName.orEmpty()
                    )
                }

                else -> {
                    step2Content
                }
            }
        }
    }

    @Immutable
    sealed class CustomsFeeInfoMainViewEvent : ViewEvent {
        data object ChangeToStepOne : CustomsFeeInfoMainViewEvent()

        data class ChangeToStepTwo(
            val listPayment: List<TaxPaymentDMO>,
            val taxPayerInfo: TaxPayerInfo,
            val taxDelegatorInfo: TaxPayerInfo,
            val modeDelegate: Boolean,
        ) : CustomsFeeInfoMainViewEvent()

        data class ChangeToStepThree(
            val step3Content: CustomsFeeInfoStep3Content,
        ) : CustomsFeeInfoMainViewEvent()

        data object PopToStepTwo : CustomsFeeInfoMainViewEvent()

        data class SetDataTransTemp(
            val transTempDMO: TransactionTemplateDMO
        ) : CustomsFeeInfoMainViewEvent()

        data class SetDataTransDetail(
            val purpose: GetTransactionDetailPurpose,
            val transactionDetailDMO: TransactionDetailDMO
        ) : CustomsFeeInfoMainViewEvent()
    }

    @Immutable
    sealed class CustomsFeeInfoMainViewEffect : SideEffect {
        data object SetTransDetailSuccessfully : CustomsFeeInfoMainViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: CustomsFeeInfoMainViewState,
        event: CustomsFeeInfoMainViewEvent
    ): Pair<CustomsFeeInfoMainViewState, CustomsFeeInfoMainViewEffect?> {

        return when (event) {
            is CustomsFeeInfoMainViewEvent.ChangeToStepOne -> {
                previousState.copy(
                    currentStep = 1
                ) to null
            }

            is CustomsFeeInfoMainViewEvent.ChangeToStepTwo -> {
                previousState.copy(
                    currentStep = 2,
                    step2Content = CustomsFeeInfoStep2Content(
                        listPayment = event.listPayment,
                        taxPayerInfo = event.taxPayerInfo,
                        taxDelegatorInfo = event.taxDelegatorInfo,
                        noteToApprover = null,
                        priorityTransaction = null,
                        customerTransactionCode = null,
                    )
                ) to null
            }

            is CustomsFeeInfoMainViewEvent.ChangeToStepThree -> {
                previousState.copy(
                    currentStep = 3,
                    step3Content = event.step3Content
                ) to null
            }

            is CustomsFeeInfoMainViewEvent.PopToStepTwo -> {
                previousState.copy(
                    currentStep = 2
                ) to null
            }

            is CustomsFeeInfoMainViewEvent.SetDataTransTemp -> {
                previousState.copy(
                    createTransactionType = CreateTransactionType.CreateFromTemplate(event.transTempDMO)
                ) to CustomsFeeInfoMainViewEffect.SetTransDetailSuccessfully
            }

            is CustomsFeeInfoMainViewEvent.SetDataTransDetail -> {
                previousState.copy(
                    createTransactionType = when(event.purpose) {
                        GetTransactionDetailPurpose.COPY_TRANSACTION ->
                            CreateTransactionType.CopyTransaction(event.transactionDetailDMO)
                        GetTransactionDetailPurpose.EDIT_TRANSACTION ->
                            CreateTransactionType.EditTransaction(event.transactionDetailDMO)
                    }
                ) to CustomsFeeInfoMainViewEffect.SetTransDetailSuccessfully
            }
        }
    }

}