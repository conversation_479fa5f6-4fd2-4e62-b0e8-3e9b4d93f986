package vn.com.bidv.feature.government.service.ui.common.views

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.theme.IBSpacing.spacing2xs
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.TransactionStatusBase

@Composable
fun GeneralInfoSection(transactionData: TransactionDetailDMO) {
    InfoCard(title = stringResource(R.string.thong_tin_chung)) {
        DetailItem(
            label = stringResource(R.string.ma_giao_dich),
            value = transactionData.transactionId
        )

        val status = transactionData.status?.let { TransactionStatusBase.fromString(it) }

        if (status != null) {
            DetailItemWithBadge(
                label = stringResource(R.string.trang_thai),
                badgeTitle = transactionData.statusName ?: "",
                badgeColor = status.color
            )
        }

        val listLabelResAndValue = listOf(
            R.string.so_tham_chieu_lo to transactionData.batchNumber,
            R.string.ma_giao_dich_khach_hang to transactionData.orgId,
            R.string.ghi_chu_den_nguoi_duyet to transactionData.noteToAuthorizer,
        )

        listLabelResAndValue.forEach { (labelRes, value) ->
            if (value != null) {
                DetailItem(label = stringResource(labelRes), value = value)
            }
        }

        if (!transactionData.denialReasonNote.isNullOrEmpty() && transactionData.status == TransactionStatusBase.REJECTED.statusCode) {
            DetailItem(
                label = stringResource(R.string.ly_do_tu_choi),
                value = transactionData.denialReasonNote
            )
        }
    }
}

@Composable
fun DetailItemWithBadge(label: String, badgeTitle: String, badgeColor: LabelColor) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = spacingXs),
        verticalArrangement = Arrangement.spacedBy(spacing2xs, Alignment.Top),
        horizontalAlignment = Alignment.Start,
    ) {
        Text(
            text = label,
            style = curLocalTypography.bodyBody_m,
            color = curLocalColorScheme.contentMainTertiary
        )

        IBankBadgeLabel(
            title = badgeTitle,
            badgeSize = LabelSize.M,
            badgeColor = badgeColor
        )
    }
}

@Preview(showBackground = true, name = "General Info Section Preview")
@Composable
fun PreviewGeneralInfoSection() {
    val mockData = TransactionDetailDMO(
        transactionId = "243643",
        paymentAmount = "1500000",
        paymentAmountText = "Một tỷ năm trăm triệu đồng",
        createdDate = "08/05/2025 10:30:45",
        status = "REJECTED",
        statusName = "Từ chối duyệt",
        payerName = "Nguyen Van A",
        approvedBy = "Tax Department",
        noteToAuthorizer = "Thanh toan abc",
        payerType = 0,
        batchNumber = "*********",
        denialReasonNote = "Chưa cung cấp đầy đủ hồ sơ theo yêu cầu",
        payerTaxCode = "*************",
        payerAddress = "Bạch Mai, Hai Bà Trưng, Hà Nội",
        admAreaName = "Địa bàn Hai Bà Trưng",
        feeMethod = "Phí khoản",
        feeTotal = "12,000,000 VND",
        taxItems = emptyList()
    )
    GeneralInfoSection(transactionData = mockData)
}