package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.government.service.model.GetTransactionDetailPurpose
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO

class ListCustomsDutiesMainReducer :
    Reducer<
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState,
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent,
            ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEffect
            > {

    @Immutable
    data class CustomsDutiesAndFeesMainViewState(
        val tabSelected: Tab = Tab.PENDING_TRANSACTIONS,
    ) : ViewState

    enum class Tab {
        ALL_TRANSACTIONS,
        PENDING_TRANSACTIONS,
    }

    @Immutable
    sealed class CustomsDutiesAndFeesMainViewEvent : ViewEvent {
        data class SelectedTab(val tab: Tab) : CustomsDutiesAndFeesMainViewEvent()
        data class OnClickCopyTransaction(val txnId: String) : CustomsDutiesAndFeesMainViewEvent()
        data class OnClickEditTransaction(val txnId: String) : CustomsDutiesAndFeesMainViewEvent()
        data class GetTransactionDetailSuccessful(
            val transactionDetailDMO: TransactionDetailDMO,
            val purpose: GetTransactionDetailPurpose,
        ) : CustomsDutiesAndFeesMainViewEvent()
    }

    @Immutable
    sealed class CustomsDutiesAndFeesMainViewEffect : SideEffect {
        data class GetTransactionDetail(val txnId: String, val purpose: GetTransactionDetailPurpose) : CustomsDutiesAndFeesMainViewEffect()
        data class GetTransactionDetailSuccessful(
            val transactionDetailDMO: TransactionDetailDMO,
            val purpose: GetTransactionDetailPurpose,
        ) : CustomsDutiesAndFeesMainViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: CustomsDutiesAndFeesMainViewState,
        event: CustomsDutiesAndFeesMainViewEvent
    ): Pair<CustomsDutiesAndFeesMainViewState, CustomsDutiesAndFeesMainViewEffect?> {
        return when (event) {
            is CustomsDutiesAndFeesMainViewEvent.SelectedTab -> {
                previousState.copy(
                    tabSelected = event.tab,
                ) to null
            }
            is CustomsDutiesAndFeesMainViewEvent.OnClickCopyTransaction -> {
                previousState to CustomsDutiesAndFeesMainViewEffect.GetTransactionDetail(event.txnId, GetTransactionDetailPurpose.COPY_TRANSACTION)
            }
            is CustomsDutiesAndFeesMainViewEvent.OnClickEditTransaction -> {
                previousState to CustomsDutiesAndFeesMainViewEffect.GetTransactionDetail(event.txnId, GetTransactionDetailPurpose.EDIT_TRANSACTION)
            }
            is CustomsDutiesAndFeesMainViewEvent.GetTransactionDetailSuccessful -> {
                previousState to
                    CustomsDutiesAndFeesMainViewEffect.GetTransactionDetailSuccessful(
                        event.transactionDetailDMO,
                        event.purpose
                    )
            }
        }
    }
}