package vn.com.bidv.feature.government.service.model

import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO
import vn.com.bidv.feature.government.service.domain.model.TransactionTemplateDMO

sealed interface CreateTransactionType {
    data object CreateNewTransaction: CreateTransactionType
    data class CreateFromTemplate(val transactionTemplateDMO: TransactionTemplateDMO):
        CreateTransactionType
    data class CopyTransaction(val transactionDetailDMO: TransactionDetailDMO):
        CreateTransactionType
    data class EditTransaction(val pendingListDMO: TransactionDetailDMO): CreateTransactionType
}