package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import vn.com.bidv.feature.government.service.domain.model.EconomicContentDMO
import vn.com.bidv.feature.government.service.ui.common.CommonDropDown
import vn.com.bidv.feature.government.service.ui.common.CommonSearchDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentReducer
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
fun EconomicCodeDropDown(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
) {
    CommonDropDown(
        labelText = stringResource(R.string.ma_noi_dung_kinh_ke),
        selectedItem = uiState.economicCodeSelected,
        displayTextSelector = {
            formatCodeName(it?.ecCode, it?.ecName)
        },
        fieldError = uiState.fieldError[AddPaymentItem.DropDownItem.EconomicCode],
        onClickEnd = {
            onEvent(
                AddPaymentReducer.AddPaymentEvent.EconomicCodeEvent.ShowEconomicCodeBottomSheet(
                    true
                )
            )
        },
        onClickClear = {
            onEvent(AddPaymentReducer.AddPaymentEvent.EconomicCodeEvent.ClearEconomicCode)
        }
    )
}

@Composable
fun ShowEconomicCodeBottomSheet(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    onValueChange: (AddPaymentItem.DropDownItem.EconomicCode, EconomicContentDMO?) -> Unit
) {
    if (uiState.showEconomicCodeBottomSheet) {
        CommonSearchDialog(
            title = stringResource(R.string.ma_noi_dung_kinh_ke),
            itemSelected = uiState.economicCodeSelected,
            listData = uiState.listEconomicCode,
            showSearchBox = true,
            searchFilter = { item, query ->
                val search = "${item.ecCode}${item.ecName}"
                VNCharacterUtil.removeAccent(search)
                    .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
            },
            onDismiss = {
                onEvent(
                    AddPaymentReducer.AddPaymentEvent.EconomicCodeEvent.ShowEconomicCodeBottomSheet(
                        false
                    )
                )
                onValueChange(AddPaymentItem.DropDownItem.EconomicCode, it)
            },
            itemTitleSelector = { formatCodeName(it.ecCode, it.ecName) },
            compareKey = { a, b -> a.ecCode == b?.ecCode }
        )
    }
}
