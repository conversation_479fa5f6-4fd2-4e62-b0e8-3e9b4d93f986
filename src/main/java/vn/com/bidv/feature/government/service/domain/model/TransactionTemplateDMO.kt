package vn.com.bidv.feature.government.service.domain.model

import com.google.gson.annotations.SerializedName

/**
 * List data
 *
 * @param taxCode Mã số thuế người nộp
 * @param altTaxCode Mã số thuế người nộp thay
 * @param payerName Tên người nộp thuế
 * @param altPayerName Tên người nộp thay
 * @param payerAddr Địa chỉ người nộp thuế
 * @param altPayerAddr Địa chỉ người nộp thay
 * @param treasuryCode Mã kho bạc
 * @param treasuryName Tên kho bạc
 * @param admAreaCode Mã địa bàn hành chính
 * @param admAreaName Tên địa bàn hành chính
 * @param revAccCode Mã tài khoản thu
 * @param revAccName Tên tài khoản thu
 * @param revAuthCode Mã cơ quan thu
 * @param revAuthName Tên cơ quan thu
 * @param payerType Lo<PERSON>i hình người nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON>, 1 - doanh nghiệp, 2 - cá nhân
 * @param debitAccNo Tài khoản trích nợ
 * @param templateId Mã mẫu giao dịch
 * @param templateName Tên mẫu giao dịch
 * @param createdDate Ngày tạo
 * @param taxItems Danh sách thông tin khoản nộp
 * @param payerTypeName
 */


data class TransactionTemplateDMO(

    @SerializedName("taxCode")
    val taxCode: String? = null,

    @SerializedName("altTaxCode")
    val altTaxCode: String? = null,

    @SerializedName("payerName")
    val payerName: String? = null,

    @SerializedName("altPayerName")
    val altPayerName: String? = null,

    @SerializedName("payerAddr")
    val payerAddr: String? = null,

    @SerializedName("altPayerAddr")
    val altPayerAddr: String? = null,

    @SerializedName("treasuryCode")
    val treasuryCode: String? = null,

    @SerializedName("treasuryName")
    val treasuryName: String? = null,

    @SerializedName("admAreaCode")
    val admAreaCode: String? = null,

    @SerializedName("admAreaName")
    val admAreaName: String? = null,

    @SerializedName("revAccCode")
    val revAccCode: String? = null,

    @SerializedName("revAccName")
    val revAccName: String? = null,

    @SerializedName("revAuthCode")
    val revAuthCode: String? = null,

    @SerializedName("revAuthName")
    val revAuthName: String? = null,

    @SerializedName("payerType")
    val payerType: Int? = null,

    @SerializedName("debitAccNo")
    val debitAccNo: String? = null,

    @SerializedName("templateId")
    val templateId: String? = null,

    @SerializedName("templateName")
    val templateName: String? = null,

    @SerializedName("createdDate")
    val createdDate: String? = null,

    @SerializedName("taxItems")
    val taxItems: List<TaxPaymentDMO>? = null,

    @SerializedName("payerTypeName")
    val payerTypeName: String? = null,

    /* Ngân hàng thụ hưởng */
    @SerializedName("benBankCode")
    val benBankCode: String? = null,

    /* Tên ngân hàng thụ hưởng */
    @SerializedName("benBankName")
    val benBankName: String? = null,

)

