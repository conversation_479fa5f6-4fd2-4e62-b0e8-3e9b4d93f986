package vn.com.bidv.feature.government.service.domain.usecase

import com.google.gson.reflect.TypeToken
import vn.com.bidv.feature.common.data.MasterDataRepository
import vn.com.bidv.feature.government.service.domain.model.BalanceAccountDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import java.lang.reflect.Type
import javax.inject.Inject

class GetDetailDebitAccountUseCase @Inject constructor(
    private val masterRepository: MasterDataRepository,
) {
    suspend fun invoke(accountNumber: String): DomainResult<BalanceAccountDMO> {
        val domain = masterRepository.getAccountDetail(
            accNo = accountNumber,
        )
        val type: Type = object : TypeToken<BalanceAccountDMO>() {}.type
        return domain.convert(type)
    }
}