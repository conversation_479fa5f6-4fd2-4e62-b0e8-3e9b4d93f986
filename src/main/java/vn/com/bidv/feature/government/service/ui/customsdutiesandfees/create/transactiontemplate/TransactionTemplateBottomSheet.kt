package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.transactiontemplate

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import vn.com.bidv.designsystem.component.IBankContextMenu
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.AllowSpecificCharactersFilter
import vn.com.bidv.designsystem.component.dataentry.IBankInputSearchBase
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMore
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.government.service.domain.model.TransactionTemplateDMO
import vn.com.bidv.feature.government.service.model.TransactionTemplateRulerFilter
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1.TaxPayerInfoReducer
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VietnamAccentRemoverFilter

@Composable
fun ShowTransactionTemplateBottomSheet(
    uiState: TaxPayerInfoReducer.TaxPaymentViewState,
    onEvent: (TaxPayerInfoReducer.TaxPaymentViewEvent) -> Unit,
    onSelected: (TransactionTemplateDMO) -> Unit
) {
    if (uiState.showTransactionTemplateBottomSheet) {
        SelectTemplateDialog {
            it?.let {
                onSelected(it)
            }
            onEvent(TaxPayerInfoReducer.TaxPaymentViewEvent.ShowTransactionTemplateBottomSheet(false))
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SelectTemplateDialog(
    onRequestDismiss: (selected: TransactionTemplateDMO?) -> Unit
) {
    val loadListViewModel: TransactionTemplateListLoadMoreViewModel = hiltViewModel()
    var textSearch by remember { mutableStateOf("") }

    IBankBottomSheet(
        title = stringResource(id = R.string.mau_giao_dich),
        onDismiss = {
            onRequestDismiss(null)
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            IBankInputSearchBase(
                Modifier
                    .fillMaxWidth()
                    .padding(vertical = spacingXs, horizontal = IBSpacing.spacingM),
                textValue = textSearch,
                filters = listOf(
                    VietnamAccentRemoverFilter(),
                    AllowSpecificCharactersFilter("., ")
                ),
                maxLength = vn.com.bidv.feature.government.service.constants.Constants.MAX_LENGTH_TEXT_INPUT_70,
                placeHolderText = stringResource(id = R.string.tim_kiem),
                onTextChange = { textFieldValue ->
                    textSearch = textFieldValue
                },
                onClickClear = {
                    loadListViewModel.sendEvent(
                        ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.UpdateRuleFilters(
                            TransactionTemplateRulerFilter(search = "")
                        )
                    )
                },
                onRequestChange = { searchValue ->
                    loadListViewModel.sendEvent(
                        ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent.UpdateRuleFilters(
                            TransactionTemplateRulerFilter(search = searchValue)
                        )
                    )
                },
            )

            ListAutoLoadMore(
                viewModel = loadListViewModel,
                emptyView = {
                    IBankEmptyState(
                        modifier = Modifier.fillMaxSize(),
                        supportingText = stringResource(R.string.khong_ton_tai_mau_giao_dich_theo_dieu_kien_tim_kiem)
                    )
                },
            ) { item ->
                val isSelected = item.data == loadListViewModel.currentItem
                item.isChecked = isSelected
                ItemTransactionTemplate(item = item) {
                    loadListViewModel.currentItem = it
                    onRequestDismiss(it)
                }
            }
        }
    }

}

@Composable
private fun ItemTransactionTemplate(
    item: ModelCheckAble<TransactionTemplateDMO>,
    onItemSelected: (TransactionTemplateDMO) -> Unit
) {
    IBankContextMenu(
        modifier = Modifier.clickable {
            onItemSelected.invoke(item.data)
        },
        isSelected = item.isChecked,
        leadingIconResId = vn.com.bidv.designsystem.R.drawable.dich_vu_cong,
        title = item.data.templateName.orEmpty(),
        description = item.data.revAuthName.orEmpty(),
        value = formatCodeName(item.data.treasuryCode, item.data.treasuryName)
    )
}