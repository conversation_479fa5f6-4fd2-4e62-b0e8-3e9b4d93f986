package vn.com.bidv.feature.government.service.domain.usecase

import com.google.gson.Gson
import vn.com.bidv.feature.common.domain.verifyFlowUseCase.VerifyByTypeTransactionUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnConfirmReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnInitPushReq
import vn.com.bidv.feature.government.service.domain.model.TransactionResDetail
import vn.com.bidv.feature.government.service.domain.model.TxnProcessResultDMO
import vn.com.bidv.feature.government.service.domain.model.TxnInitPushDMO
import vn.com.bidv.feature.government.service.model.TxnInitPushType
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class PushCustomsDutiesTransactionUseCase @Inject constructor(
    private val governmentServiceRepository: GovernmentServiceRepository
) : VerifyByTypeTransactionUseCase {
    override suspend fun initTransaction(input: InputVerifyTransaction): DomainResult<InitVerifyTransactionResponse> {
        val result = governmentServiceRepository.initPushTransaction(
            TxnInitPushReq(
                txnIds = input.txnIds,
                type = TxnInitPushReq.Type.PUSH
            )
        )
        return result.convert(InitVerifyTransactionResponse::class.java)
    }

    override suspend fun initCreateTransaction(input: InputVerifyCreateTransaction): DomainResult<InitVerifyTransactionResponse> {
        val txnInitPushDMO = Gson().fromJson(input.dataString, TxnInitPushDMO::class.java)
        val type = when (txnInitPushDMO.type) {
            TxnInitPushType.PUSH_EDIT -> TxnInitPushReq.Type.PUSH_EDIT
            TxnInitPushType.PUSH_SAVE -> TxnInitPushReq.Type.PUSH_SAVE
            else -> TxnInitPushReq.Type.PUSH_SAVE
        }
        val result = governmentServiceRepository.initPushTransaction(
            TxnInitPushReq(
                transKey = txnInitPushDMO.transKey,
                type = type,
            )
        )
        return result.convert(InitVerifyTransactionResponse::class.java)
    }

    override suspend fun verifyTransaction(
        initResponse: InitVerifyTransactionResponse,
        reqValue: String?
    ): DomainResult<String> {
        val request = TxnConfirmReq(
            transKey = initResponse.transKey ?: "",
            confirmValue = reqValue ?: ""
        )

        val result = governmentServiceRepository.confirmPushTransaction(request)

        return result.convert {
            val txnProcessResultDMO = TxnProcessResultDMO(
                treasuryCode = treasuryCode,
                admAreaCode = admAreaCode,
                revAccCode = revAccCode,
                revAuthCode = revAuthCode,
                treasuryName = treasuryName,
                admAreaName = admAreaName,
                revAccName = revAccName,
                revAuthName = revAuthName,
                failTxns = failTxns?.map { failTxn ->
                    TransactionResDetail(
                        txnId = failTxn.txnId,
                        message = failTxn.message,
                        code = failTxn.code
                    )
                },
                txnId = txnId,
                totalAmount = totalAmount,
                ccy = ccy,
                feeTotal = feeTotal,
                feeCcy = feeCcy,
                feeOpt = feeOpt,
                createdDate = createdDate,
                debitAccNo = debitAccNo,
                debitAccName = debitAccName,
                total = total,
                totalSuccess = totalSuccess,
                totalFail = totalFail,
                totalAmountText = totalAmountText
            )
            Gson().toJson(txnProcessResultDMO)
        }
    }
}