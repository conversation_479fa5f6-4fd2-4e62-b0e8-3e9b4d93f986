package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import vn.com.bidv.feature.government.service.domain.model.ChapterDMO
import vn.com.bidv.feature.government.service.ui.common.CommonDropDown
import vn.com.bidv.feature.government.service.ui.common.CommonSearchDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentReducer
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
fun ChapterCodeDropDown(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
) {
    CommonDropDown(
        labelText = stringResource(R.string.ma_chuong),
        selectedItem = uiState.chapterCodeSelected,
        displayTextSelector = {
            formatCodeName(it?.chapterCode, it?.chapterName)
        },
        fieldError = uiState.fieldError[AddPaymentItem.DropDownItem.ChapterCode],
        onClickEnd = {
            onEvent(
                AddPaymentReducer.AddPaymentEvent.ChapterCodeEvent.ShowChapterCodeBottomSheet(
                    true
                )
            )
        },
        onClickClear = {
            onEvent(AddPaymentReducer.AddPaymentEvent.ChapterCodeEvent.ClearChapterCode)
        }
    )
}

@Composable
fun ShowChapterCodeBottomSheet(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    onValueChange: (AddPaymentItem.DropDownItem.ChapterCode, ChapterDMO?) -> Unit
) {
    if (uiState.showChapterCodeBottomSheet) {
        CommonSearchDialog(
            title = stringResource(R.string.ma_chuong),
            itemSelected = uiState.chapterCodeSelected,
            listData = uiState.listChapterCode,
            showSearchBox = true,
            searchFilter = { item, query ->
                val search = "${item.chapterCode}${item.chapterName}"
                VNCharacterUtil.removeAccent(search)
                    .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
            },
            onDismiss = {
                onEvent(
                    AddPaymentReducer.AddPaymentEvent.ChapterCodeEvent.ShowChapterCodeBottomSheet(
                        false
                    )
                )
                onValueChange(AddPaymentItem.DropDownItem.ChapterCode, it)
            },
            itemTitleSelector = { formatCodeName(it.chapterCode, it.chapterName) },
            compareKey = { a, b -> a.chapterCode == b?.chapterCode }
        )
    }
}


