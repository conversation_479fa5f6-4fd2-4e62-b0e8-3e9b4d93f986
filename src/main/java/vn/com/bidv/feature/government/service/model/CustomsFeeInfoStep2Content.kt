package vn.com.bidv.feature.government.service.model

import kotlinx.serialization.Serializable
import vn.com.bidv.feature.government.service.domain.model.AdministrativeAreaDMO
import vn.com.bidv.feature.government.service.domain.model.BalanceAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAuthorityDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO

@Serializable
data class CustomsFeeInfoStep2Content(
    val listPayment: List<TaxPaymentDMO>,
    val taxPayerInfo: TaxPayerInfo,
    val taxDelegatorInfo: TaxPayerInfo,

    val debitAccountSelected: BalanceAccountDMO? = null,
    val treasurySelected: TreasuryDMO? = null,
    val revenueAuthoritySelected: RevenueAuthorityDMO? = null,
    val revenueAccountSelected: RevenueAccountDMO? = null,
    val administrativeAreaSelected: AdministrativeAreaDMO? = null,
    val taxPayerEntitySelected: TaxPayerEntity = TaxPayerEntity.BUSINESS,

    val noteToApprover: String?,
    val priorityTransaction: Boolean?,
    val customerTransactionCode: String?,

    val benBankCode: String? = null,
    val benBankName: String? = null,
    val editingTransactionId: String? = null,
)