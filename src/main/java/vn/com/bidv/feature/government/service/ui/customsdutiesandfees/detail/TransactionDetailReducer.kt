package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.detail

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO

class TransactionDetailReducer :
    Reducer<TransactionDetailReducer.TransactionDetailViewState,
            TransactionDetailReducer.TransactionDetailViewEvent,
            TransactionDetailReducer.TransactionDetailEffect> {

    @Immutable
    data class TransactionDetailViewState(
        val transactionId: String = "",
        val transactionData: TransactionDetailDMO? = null
    ) : ViewState

    @Immutable
    sealed class TransactionDetailViewEvent : ViewEvent {
        data class GetTransactionDetail(val transactionId: String) : TransactionDetailViewEvent()
        data class FillTransactionDetail(val transactionData: TransactionDetailDMO?) :
            TransactionDetailViewEvent()

        data class UpdateStatusTransaction(val transactionId: String) :
            TransactionDetailViewEvent()
    }

    @Immutable
    sealed class TransactionDetailEffect : SideEffect {
        data class FetchTransactionDetail(val transactionId: String) : TransactionDetailEffect()
        data class UpdateStatusTransaction(val transactionId: String) : TransactionDetailEffect()
    }

    override fun reduce(
        previousState: TransactionDetailViewState,
        event: TransactionDetailViewEvent
    ): Pair<TransactionDetailViewState, TransactionDetailEffect?> {
        return when (event) {
            is TransactionDetailViewEvent.GetTransactionDetail -> {
                previousState to TransactionDetailEffect.FetchTransactionDetail(event.transactionId)
            }

            is TransactionDetailViewEvent.FillTransactionDetail -> {
                previousState.copy(transactionData = event.transactionData) to null
            }

            is TransactionDetailViewEvent.UpdateStatusTransaction -> {
                previousState to TransactionDetailEffect.UpdateStatusTransaction(event.transactionId)
            }
        }
    }
}