package vn.com.bidv.feature.government.service.ui.common.views

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.card.IBankCardTransfer
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBSpacing.spacing2xs
import vn.com.bidv.designsystem.theme.IBSpacing.spacing4xl
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.government.service.model.BankCardInfoDMO
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO

@Composable
fun BankCardSection(
    transactionData: TransactionDetailDMO
) {
    var firstCardHeight by remember { mutableIntStateOf(0) }
    val spacing = with(LocalDensity.current) { (spacing4xl).roundToPx() }
    val bankBIDVCardInfo = BankCardInfoDMO(
        title = "BIDV",
        iconPainterResId = R.mipmap.bidv,
        iconContentDescription = "BIDV",
        listDescription = listOf(transactionData.debitAccName ?: ""),
        subtitle = transactionData.debitAccNumber ?: "",
    )
    val beneficiaryBankCardInfo = BankCardInfoDMO(
        title = transactionData.treasuryName ?: "",
        iconContentDescription = transactionData.treasuryName ?: "",
        iconPainterResId = R.drawable.dich_vu_cong,
        listDescription = listOf(transactionData.authorityName ?: ""),
        subtitle = transactionData.revAccCode ?: "",
    )
    Box {
        Column {
            Box {
                IBankCardTransfer(
                    modifier = Modifier
                        .wrapContentHeight()
                        .onGloballyPositioned { coordinates ->
                            firstCardHeight = coordinates.size.height
                        },
                    title = bankBIDVCardInfo.title,
                    icon = {
                        Image(
                            painter = painterResource(id = bankBIDVCardInfo.iconPainterResId),
                            contentDescription = bankBIDVCardInfo.iconContentDescription
                        )
                    },
                    subTitle = bankBIDVCardInfo.subtitle,
                    listDescription = bankBIDVCardInfo.listDescription,
                    isShowDropDownIcon = false
                )
            }
            Spacer(modifier = Modifier.height(spacingXs))

            IBankCardTransfer(
                title = beneficiaryBankCardInfo.title,
                icon = {
                    Box(
                        modifier = Modifier
                            .size(IBSpacing.spacing3xl)
                            .clip(CircleShape)
                            .background(LocalColorScheme.current.bgMainPrimary),
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = beneficiaryBankCardInfo.iconPainterResId),
                            contentDescription = beneficiaryBankCardInfo.iconContentDescription
                        )
                    }
                },
                subTitle = beneficiaryBankCardInfo.subtitle,
                listDescription = beneficiaryBankCardInfo.listDescription,
                bgBrush = IBGradient.color_grd_card_nh,
                isShowLogo = false,
                isShowDropDownIcon = false
            )
        }

        Box(
            modifier = Modifier
                .wrapContentSize()
                .align(Alignment.TopCenter)
                .offset {
                    IntOffset(
                        x = 0,
                        y = (firstCardHeight / 2) + spacing
                    )
                }
                .size(IBSpacing.spacing3xl)
                .clip(CircleShape)
                .background(LocalColorScheme.current.bgMainTertiary),
            contentAlignment = Alignment.Center
        ) {
            Box(
                modifier = Modifier
                    .size(IBSpacing.spacing2xl)
                    .clip(CircleShape)
                    .background(LocalColorScheme.current.bgMainPrimary),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    modifier = Modifier.padding(spacing2xs),
                    painter = painterResource(R.drawable.arrow2_bottom),
                    contentDescription = "",
                    tint = LocalColorScheme.current.contentMainPrimary,
                )
            }
        }
    }
}

@Composable
fun AmountSection(amount: String, amountText: String) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
            .wrapContentHeight()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL),
                    color = LocalColorScheme.current.bgMainTertiary
                )
                .padding(
                    horizontal = IBSpacing.spacingM,
                    vertical = IBSpacing.spacingM
                )
                .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL)),
            verticalArrangement = Arrangement.spacedBy(spacingXs)
        ) {
            Text(
                text = stringResource(vn.com.bidv.localization.R.string.so_tien_nop_nsnn),
                style = curLocalTypography.bodyBody_l,
                color = curLocalColorScheme.contentMainTertiary
            )

            Text(
                text = amount,
                style = curLocalTypography.headlineHeadline_s,
            )

            Text(
                text = amountText,
                style = curLocalTypography.bodyBody_l
            )
        }
    }
}



@Preview(showBackground = true, name = "Bank Card Section Preview")
@Composable
fun PreviewBankCardSection() {
    val mockData = TransactionDetailDMO(
        transactionId = "TXN123",
        authorityCode = "7111",
        revAccCode = "7112",
        authorityName = "CHI CUC HAI QUAN HAI PHONG",
        debitAccName = "CONG TY CO PHAN DAU TU QUANG CAO",
        debitAccNumber = "***************",
        treasuryName = "KHO BAC NHA NUOC HAI PHONG"
    )
    BankCardSection(mockData)
}

@Preview(showBackground = true, name = "Amount Section Preview")
@Composable
fun PreviewAmountSection() {
    AmountSection(amount = "1,600,000,000 VND", amountText = "Một tỷ sáu trăm triệu đồng")
}
