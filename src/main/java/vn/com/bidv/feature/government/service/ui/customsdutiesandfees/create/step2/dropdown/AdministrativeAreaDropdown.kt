package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import vn.com.bidv.feature.government.service.domain.model.AdministrativeAreaDMO
import vn.com.bidv.feature.government.service.model.FieldStatus
import vn.com.bidv.feature.government.service.model.TaxPaymentFormField
import vn.com.bidv.feature.government.service.model.TaxPaymentFormFieldValidateError
import vn.com.bidv.feature.government.service.ui.common.CommonDropDown
import vn.com.bidv.feature.government.service.ui.common.CommonSearchDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.TaxPaymentInfoUiState
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.TaxPaymentInfoViewEvent
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
internal fun AdministrativeAreaDropdown(
    uiState: TaxPaymentInfoUiState,
    onEvent: (TaxPaymentInfoViewEvent) -> Unit,
)  {
    CommonDropDown(
        required = true,
        showClearButton = false,
        labelText = stringResource(R.string.dia_ban_hanh_chinh),
        selectedItem = uiState.administrativeAreaSelected,
        displayTextSelector = { formatCodeName(it?.admAreaCode, it?.admAreaName) },
        fieldError = if (uiState.formError == TaxPaymentFormFieldValidateError.RequiredFieldNotFilled && uiState.administrativeAreaSelected == null) {
            FieldStatus.INVALID
        } else null,
        onClickClear = {
            onEvent(TaxPaymentInfoViewEvent.UpdateSelectedAdministrativeArea(null))
        },
        onClickEnd = {
            onEvent(
                TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
                    TaxPaymentFormField.ADM_AREA_CODE,
                    true
                )
            )
        }
    )
}
@Composable
internal fun AdministrativeAreaSearchDialog(
    listData: List<AdministrativeAreaDMO>,
    itemSelected: AdministrativeAreaDMO?,
    onDismiss: (AdministrativeAreaDMO?) -> Unit,
) {
    CommonSearchDialog(
        title = stringResource(R.string.dia_ban_hanh_chinh),
        itemSelected = itemSelected,
        listData = listData,
        showSearchBox = true,
        searchFilter = { item, query ->
            val search = "${item.admAreaCode}${item.admAreaName}"
            VNCharacterUtil.removeAccent(search)
                .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
        },
        onDismiss = onDismiss,
        itemTitleSelector = { formatCodeName(it.admAreaCode, it.admAreaName) }
    )
}