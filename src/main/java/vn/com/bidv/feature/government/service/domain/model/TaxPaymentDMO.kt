package vn.com.bidv.feature.government.service.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.government.service.model.IViewTransaction
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import java.util.UUID

/**
 * Model for inquiry tax payment
 *
 * @param eiTypeCode Mã loại hình xuất nhập khẩu
 * @param eiTypeName Tên loại hình xuất nhập khẩu
 * @param taxTypeCode Mã sắc thuế
 * @param taxTypeName Tên sắc thuế
 * @param ccCode Mã loại tiền hải quan
 * @param ccName Tên loại tiền hải quan
 * @param chapterCode Mã chương
 * @param chapterName Tên chương
 * @param ecCode Mã nội dung kinh tế
 * @param ecName Tên nội dung kinh tế
 * @param amount Số tiền
 * @param ccy Đơn vị tiền tệ
 * @param declarationDate Ngày tờ khai
 * @param declarationNo Số tờ khai
 * @param transDesc Diễn giải giao dịch
 * @param payerType Loại hình người nộp thuế: 0 - Không xác định, 1 - doanh nghiệp, 2 - cá nhân
 * @param payerTypeName Tên loại hình người nộp thuế
 * @param treasuryCode Mã kho bạc
 * @param treasuryName Tên kho bạc
 * @param admAreaCode Mã địa bàn hành chính
 * @param admAreaName Tên địa bàn hành chính
 * @param revAccCode Mã tài khoản thu
 * @param revAccName Tên tài khoản thu
 * @param revAuthCode Mã cơ quan thu
 * @param revAuthName Tên cơ quan thu
 * @param benBankCode Ngân hàng thụ hưởng
 * @param benBankName Tên ngân hàng thụ hưởng
 */
@Serializable
data class TaxPaymentDMO(
    @SerializedName("eiTypeCode")
    val eiTypeCode: String,

    @SerializedName("eiTypeName")
    val eiTypeName: String?,

    @SerializedName("taxTypeCode")
    val taxTypeCode: String,

    @SerializedName("taxTypeName")
    val taxTypeName: String?,

    @SerializedName("ccCode")
    val ccCode: String,

    @SerializedName("ccName")
    val ccName: String?,

    @SerializedName("chapterCode")
    val chapterCode: String,

    @SerializedName("chapterName")
    val chapterName: String?,

    @SerializedName("ecCode")
    val ecCode: String,

    @SerializedName("ecName")
    val ecName: String?,

    @SerializedName("amount")
    val amount: String,

    @SerializedName("ccy")
    val ccy: String,

    @SerializedName("declarationDate")
    val declarationDate: String,

    @SerializedName("declarationNo")
    val declarationNo: String,

    @SerializedName("transDesc")
    val transDesc: String,

    @SerializedName("payerType")
    val payerType: Int? = null,

    @SerializedName("payerTypeName")
    val payerTypeName: String? = null,

    @SerializedName("treasuryCode")
    val treasuryCode: String? = null,

    @SerializedName("treasuryName")
    val treasuryName: String? = null,

    @SerializedName("admAreaCode")
    val admAreaCode: String? = null,

    @SerializedName("admAreaName")
    val admAreaName: String? = null,

    @SerializedName("revAccCode")
    val revAccCode: String? = null,

    @SerializedName("revAccName")
    val revAccName: String? = null,

    @SerializedName("revAuthCode")
    val revAuthCode: String? = null,

    @SerializedName("revAuthName")
    val revAuthName: String? = null,

    @SerializedName("benBankCode")
    val benBankCode: String? = null,

    @SerializedName("benBankName")
    val benBankName: String? = null,

    @SerializedName("uniqueId")
    val uniqueId: String? = UUID.randomUUID().toString()
): IViewTransaction {

    override fun getHeaderString() = declarationNo

    override fun getValueTitle() = formatCodeName(eiTypeCode, eiTypeName)

    override fun getValueTaxDescription() = ecName ?: "--"

    override fun getValueAmount() = amount

    override fun getValueCcy() = ccy

    override fun getValueDate(): Pair<String, String> {
        return SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD to declarationDate
    }
}