package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.google.gson.Gson
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.common.utils.unpackV2
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.IBankSectionHeader
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.LeadingType
import vn.com.bidv.designsystem.component.datadisplay.tooltip.IBankTooltip
import vn.com.bidv.designsystem.component.datadisplay.tooltip.TooltipPosition
import vn.com.bidv.designsystem.component.dataentry.AllowSpecificCharactersFilter
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankCheckBox
import vn.com.bidv.designsystem.component.dataentry.IBankInputFieldBase
import vn.com.bidv.designsystem.component.dataentry.RemoveDoubleSpaceFilter
import vn.com.bidv.designsystem.component.dataentry.RemoveTabEnterFilter
import vn.com.bidv.designsystem.component.dataentry.RemoveVietnameseAccentFilter
import vn.com.bidv.designsystem.component.dataentry.datacard.IBankDataCard
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.feature.government.service.model.AddPaymentScreenArguments
import vn.com.bidv.feature.government.service.model.FailedDialogContent
import vn.com.bidv.feature.government.service.model.TaxPaymentApiCall
import vn.com.bidv.feature.government.service.model.TaxPaymentFormField
import vn.com.bidv.feature.government.service.model.TaxPaymentFormFieldValidateError
import vn.com.bidv.feature.government.service.model.TaxPaymentValidateFailedPopupType
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute
import vn.com.bidv.feature.government.service.ui.common.StepProgressBar
import vn.com.bidv.feature.government.service.ui.common.TaxPaymentCard
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.CustomsFeeInfoMainReducer
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.CustomsFeeInfoMainViewModel
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.AdministrativeAreaDropdown
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.AdministrativeAreaSearchDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.DebitAccountPickerDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.DebitAccountPickerDropdownCard
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.RevenueAccountDropdown
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.RevenueAccountSearchDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.RevenueAuthorityDropdown
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.RevenueAuthoritySearchDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.TaxPayerEntityDialog
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.TaxPayerEntityDropdown
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.TreasuryDropdown
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2.dropdown.TreasurySearchDialog
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun TaxPaymentInfoScreen(
    navHostController: NavHostController,
) {
    val context = LocalContext.current
    val createFlowViewModel: CustomsFeeInfoMainViewModel = hiltViewModel()
    val (createFlowUiState, createFlowOnEvent, _) = createFlowViewModel.unpackV2()
    val viewModel: TaxPaymentInfoViewModel = hiltViewModel()
    var failedDialogContent by remember {
        mutableStateOf<FailedDialogContent<TaxPaymentApiCall>?>(null)
    }

    var showPopupDeletePayment by remember { mutableStateOf<TaxPaymentDMO?>(null) }
    BaseScreen(
        viewModel = viewModel,
        navController = navHostController,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(R.string.chi_tiet_thanh_toan),
            showHomeIcon = true,
            onNavigationClick = {
                createFlowOnEvent(
                    CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepOne
                )
                viewModel.sendEvent(TaxPaymentInfoViewEvent.ClearData)
            }
        ),
        handleSideEffect = {
            when (it) {
                is TaxPaymentInfoSideEffect.OnCallApiError -> {
                    failedDialogContent =
                        FailedDialogContent.fromRaw(it.errorCode, it.errorMessage, it.type)
                }

                is TaxPaymentInfoSideEffect.ValidateBeforeContinueFailed -> {
                    when (it.popupType) {
                        TaxPaymentValidateFailedPopupType.PAYMENTS_NOT_FILLED -> {
                            viewModel.showSnackBar(
                                IBankSnackBarInfo(
                                    message = context.getString(R.string.thong_tin_khoan_nop_chua_day_du_vui_long_kiem_tra_lai_thong_tin),
                                    primaryButtonText = context.getString(R.string.close)
                                )
                            )
                        }
                    }
                }

                is TaxPaymentInfoSideEffect.ValidateServerSideSuccess -> {
                    createFlowOnEvent(
                        CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepThree(
                            it.customsFeeInfoStep3Content
                        )
                    )
                }

                is TaxPaymentInfoSideEffect.DeletePaymentSuccess -> {
                    viewModel.showSnackBar(
                        IBankSnackBarInfo(
                            message = context.getString(R.string.xoa_thanh_cong),
                            primaryButtonText = context.getString(R.string.close)
                        )
                    )
                    if (it.shouldPopToPreviousStep) {
                        createFlowViewModel.sendEvent(
                            CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepOne
                        )
                    }
                }

                else -> {
                    /*handle nothing*/
                }
            }
        }
    ) { uiState, onEvent ->
        CollectSideEffect(viewModel.subscribeShareData(Constants.EDIT_PAYMENT_DATA)) {
            val addPaymentData = Gson().fromJson(it.data, TaxPaymentDMO::class.java)
            if (addPaymentData != null) {
                onEvent(
                    TaxPaymentInfoViewEvent.UpdatePaymentItem(addPaymentData)
                )
            }
        }

        Column {
            if (!uiState.isInitialized) {
                val step2Content = createFlowUiState.value.getStep2Content()
                onEvent(
                    TaxPaymentInfoViewEvent.InitScreen(
                        step2Content
                ))
            }
            StepProgressBar(Constants.FLOW_CREATE_STEP_TWO)
            TaxPaymentInfoContent(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(rememberScrollState()),
                navHostController = navHostController,
                uiState = uiState,
                onEvent = onEvent,
                onDeleteClick = {
                    showPopupDeletePayment = it
                },
                onEditClick = { payment ->
                    navHostController.navigate(GovernmentServiceRoute.AddPaymentRoute(
                        AddPaymentScreenArguments(
                            data = payment,
                            isEditPayment = true,
                            listPayment = uiState.listPayment,
                        )
                    ))
                }
            )
            IBankActionBar(
                buttonPositive = DialogButtonInfo(stringResource(R.string.tiep_tuc)) {
                    onEvent(TaxPaymentInfoViewEvent.ValidateBeforeContinue)
                },
                isVertical = false,
                leadingIcon = ImageVector.vectorResource(vn.com.bidv.designsystem.R.drawable.khoan_phai_thu),
                title = stringResource(R.string.tong_tien),
                description = uiState.listPayment.sumOf { it.amount.toLong() }
                    .toString().formatMoney(SdkBaseConstants.MoneyCurrencyConstants.VND, true),
            )
        }

        failedDialogContent?.let {
            IBankModalConfirm(
                modalConfirmType = ModalConfirmType.Error,
                title = stringResource(R.string.loi),
                isHtmlContent = true,
                supportingText = it.message,
                listDialogButtonInfo = buildList {
                    if (it.showButtonRetry) {
                        add(
                            DialogButtonInfo(
                            label = stringResource(R.string.thu_lai),
                            onClick = { onEvent(TaxPaymentInfoViewEvent.RetryFetchData(it.type)) }
                        ))
                    }
                    if (!it.showButtonRetry || it.type == TaxPaymentApiCall.LIST_DEBIT_ACCOUNT) {
                        add(
                            DialogButtonInfo(
                                label = stringResource(R.string.dong),
                            )
                        )
                    }
                },
                onDismissRequest = {
                    failedDialogContent = null
                },
            )
        }

        showPopupDeletePayment?.let {
            val style = if (uiState.listPayment.size == 1) {
                ModalConfirmType.Warning to stringResource(R.string.giao_dich_can_co_it_nhat_1_khoan_nop_de_tiep_tuc_quy_khach_co_chac_chan_xoa_khoan_nop_khong)
            } else {
                ModalConfirmType.Question to stringResource(R.string.quy_khach_co_chac_chan_xoa_khoan_nop)
            }

            IBankModalConfirm(
                modalConfirmType = style.first,
                title = stringResource(R.string.xac_nhan_xoa),
                supportingText = style.second,
                listDialogButtonInfo = listOf(
                    DialogButtonInfo(
                        label = stringResource(R.string.xac_nhan),
                        onClick = {
                            onEvent(TaxPaymentInfoViewEvent.DeletePayment(it))
                        }
                    ),
                    DialogButtonInfo(
                        label = stringResource(R.string.huy),
                    )
                ),
                onDismissRequest = {
                    showPopupDeletePayment = null
                }
            )
        }
    }
}

@Composable
private fun TaxPaymentInfoContent(
    modifier: Modifier = Modifier,
    uiState: TaxPaymentInfoUiState,
    onEvent: (TaxPaymentInfoViewEvent) -> Unit,
    navHostController: NavHostController,
    onDeleteClick: (TaxPaymentDMO) -> Unit,
    onEditClick: (TaxPaymentDMO) -> Unit
) {
    val colorScheme = LocalColorScheme.current

    Column(
        modifier = modifier
            .padding(IBSpacing.spacingM),
        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM),
    ) {
        DebitAccountPickerDropdownCard(
            uiState =
                uiState,
            onEvent = onEvent,
        )
        IBankDataCard(
            showCheckbox = false,
            isChecked = false,
            cardHeader = {
                IBankSectionHeader(
                    shLeadingType = LeadingType.Dash(),
                    shSectionTitle = stringResource(R.string.thong_tin_thu_huong),
                    thumbContent = {}
                )
            },
            cardFooter = {},
            cardContent = {
                Column(
                    modifier = Modifier.padding(
                        bottom = IBSpacing.spacingM,
                        start = IBSpacing.spacingM,
                        end = IBSpacing.spacingM
                    ),
                    verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM),
                ) {
                    TreasuryDropdown(uiState, onEvent)
                    IBankInputFieldBase(
                        required = true,
                        placeholderText = stringResource(R.string.ngan_hang_thu_huong),
                        text = formatCodeName(uiState.benBankCode, uiState.benBankName),
                        onValueChange = {},
                        state = IBFrameState.DISABLE(colorScheme)
                    )
                    RevenueAuthorityDropdown(uiState, onEvent)
                    RevenueAccountDropdown(uiState, onEvent)
                    AdministrativeAreaDropdown(uiState, onEvent)
                    if (uiState.administrativeAreaSelected?.admAreaName != null) {
                        IBankInputFieldBase(
                            required = true,
                            placeholderText = stringResource(R.string.ten_dia_ban_hanh_chinh),
                            text = uiState.administrativeAreaSelected.admAreaName,
                            onValueChange = {},
                            state = IBFrameState.DISABLE(colorScheme)
                        )
                    }
                    TaxPayerEntityDropdown(uiState, onEvent)
                }
            },
            onCheckedChange = {}
        )

        IBankDataCard(
            showCheckbox = false,
            isChecked = false,
            cardHeader = {
                IBankSectionHeader(
                    shLeadingType = LeadingType.Dash(),
                    shSectionTitle = stringResource(R.string.thong_tin_khoan_nop),
                    thumbContent = {}
                )
            },
            cardFooter = {},
            cardContent = {
                Column(
                    modifier = Modifier.padding(
                        bottom = IBSpacing.spacingM,
                        start = IBSpacing.spacingM,
                        end = IBSpacing.spacingM
                    ),
                    verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS),
                ) {
                    uiState.listPayment.forEach {
                        val state =
                            if (uiState.formError is TaxPaymentFormFieldValidateError.PaymentsFieldIsNotCompleted &&
                                uiState.formError.payment.uniqueId == it.uniqueId
                            ) {
                                IBFrameState.ERROR(colorScheme)
                            } else {
                                IBFrameState.DEFAULT(colorScheme)
                            }
                        TaxPaymentCard(
                            showCheckbox = false,
                            transactionsDMO = ModelCheckAble(it),
                            actions = listOf(ActionType.EDIT, ActionType.Delete),
                            state = state,
                            handleAction = { actionType ->
                                when (actionType) {
                                    ActionType.Delete -> {
                                        onDeleteClick(it)
                                    }

                                    ActionType.EDIT -> {
                                        onEditClick(it)
                                    }

                                    else -> {
                                        /*do nothing*/
                                    }
                                }
                            },
                            onCheckedChange = {},
                            onClick = {
                                val newTaxPaymentDMO = it.copy(
                                    treasuryName = uiState.treasurySelected?.treasuryName,
                                    treasuryCode = uiState.treasurySelected?.treasuryCode,
                                    revAuthName = uiState.revenueAuthoritySelected?.revAuthName,
                                    revAuthCode = uiState.revenueAuthoritySelected?.revAuthCode,
                                    revAccName = uiState.revenueAccountSelected?.revAccName,
                                    revAccCode = uiState.revenueAccountSelected?.revAccCode,
                                    admAreaName = uiState.administrativeAreaSelected?.admAreaName,
                                    admAreaCode = uiState.administrativeAreaSelected?.admAreaCode,
                                )
                                navHostController.navigate(
                                    GovernmentServiceRoute.TaxPaymentDetailRoute(
                                        args = newTaxPaymentDMO
                                    )
                                )
                            }
                        )
                    }
                }
            },
            onCheckedChange = {}
        )

        var cardOtherExpanded by remember { mutableStateOf(true) }
        IBankDataCard(
            showCheckbox = false,
            isChecked = false,
            cardHeader = {
                IBankSectionHeader(
                    shLeadingType = LeadingType.Dash(),
                    shSectionTitle = stringResource(R.string.khac),
                    thumbContent = {
                        IconButton(
                            onClick = {
                                cardOtherExpanded = !cardOtherExpanded
                            }
                        ) {
                            Icon(
                                imageVector = ImageVector.vectorResource(id =
                                    if (cardOtherExpanded)
                                        vn.com.bidv.designsystem.R.drawable.arrow_top_outline
                                    else
                                        vn.com.bidv.designsystem.R.drawable.arrow_bottom_outline),
                                contentDescription = null,
                                tint = colorScheme.contentMainSecondary
                            )
                        }
                    }
                )
            },
            cardFooter = {},
            cardContent = {
                if (cardOtherExpanded) {
                    Column(
                        Modifier.padding(
                            bottom = IBSpacing.spacingM,
                            start = IBSpacing.spacingM,
                            end = IBSpacing.spacingM
                        ),
                        verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS)
                    ) {
                        Row(
                            modifier = Modifier.padding(bottom = 8.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            IBankCheckBox(
                                checked = uiState.priorityTransaction,
                            ) {
                                onEvent(
                                    TaxPaymentInfoViewEvent.TogglePriorityTransaction(it.checked)
                                )
                            }
                            Text(
                                text = stringResource(R.string.giao_dich_uu_tien),
                                style = LocalTypography.current.bodyBody_l,
                                color = colorScheme.contentMainSecondary,
                                modifier = Modifier.padding(horizontal = IBSpacing.spacingS)
                            )
                            IBankTooltip(
                                isSupportingText = true,
                                subContent = {
                                    Text(
                                        text = stringResource(R.string.giao_dich_danh_dau_de_cac_cap_duyet_uu_tien_xu_ly),
                                        style = LocalTypography.current.bodyBody_m,
                                        color = colorScheme.contentOn_specialPrimary
                                    )
                                },
                                tooltipPosition = TooltipPosition.BELOW
                            ) { modifier ->
                                Icon(
                                    imageVector = ImageVector.vectorResource(vn.com.bidv.designsystem.R.drawable.information_circle_outline),
                                    contentDescription = null,
                                    modifier = modifier
                                )
                            }
                        }

                        IBankInputFieldBase(
                            placeholderText = stringResource(R.string.ma_giao_dich_khach_hang),
                            text = uiState.customerTransactionCode,
                            maxLengthText = Constants.MAX_LENGTH_TEXT_INPUT_30,
                            inputType = KeyboardOptions(keyboardType = KeyboardType.Password),
                            filters = listOf(
                                RemoveVietnameseAccentFilter(),
                                AllowSpecificCharactersFilter("-_ "),
                                RemoveDoubleSpaceFilter(),
                                RemoveTabEnterFilter()
                            ),
                            onClickClear = {
                                onEvent(TaxPaymentInfoViewEvent.UpdateCustomerTransactionCode(""))
                            },
                            onFocusChange = {
                                if (!it) {
                                    TaxPaymentInfoViewEvent.UpdateNoteToApproverString(uiState.noteToApprover.trim())
                                }
                            },
                        ) {
                            onEvent(TaxPaymentInfoViewEvent.UpdateCustomerTransactionCode(it.text))
                        }

                        IBankInputFieldBase(
                            placeholderText = stringResource(R.string.ghi_chu_toi_nguoi_duyet),
                            text = uiState.noteToApprover,
                            maxLengthText = Constants.MAX_LENGTH_TEXT_INPUT_100,
                            isShowMaxLength = true,
                            inputType = KeyboardOptions(keyboardType = KeyboardType.Password),
                            filters = listOf(
                                RemoveVietnameseAccentFilter(),
                                AllowSpecificCharactersFilter("-_ "),
                                RemoveDoubleSpaceFilter(),
                                RemoveTabEnterFilter()
                            ),
                            onFocusChange = {
                                if (!it) {
                                    TaxPaymentInfoViewEvent.UpdateNoteToApproverString(uiState.noteToApprover.trim())
                                }
                            },
                            onClickClear = {
                                onEvent(TaxPaymentInfoViewEvent.UpdateNoteToApproverString(""))
                            }
                        ) {
                            onEvent(TaxPaymentInfoViewEvent.UpdateNoteToApproverString(it.text))
                        }
                    }
                }
            },
            onCheckedChange = {}
        )
    }

    if (uiState.showTreasuryBottomSheet && uiState.listTreasury != null) {
        TreasurySearchDialog(
            listData = uiState.listTreasury,
            itemSelected = uiState.treasurySelected,
            onDismiss = { selectedItem ->
                if (selectedItem != uiState.treasurySelected && selectedItem != null) {
                    onEvent(TaxPaymentInfoViewEvent.UpdateSelectedTreasury(selectedItem))
                } else {
                    onEvent(
                        TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
                            TaxPaymentFormField.TREASURY_CODE,
                            false
                        )
                    )
                }
            }
        )
    }

    if (uiState.showRevenueAuthorityBottomSheet && uiState.listRevenueAuthority != null) {
        RevenueAuthoritySearchDialog(
            listData = uiState.listRevenueAuthority,
            itemSelected = uiState.revenueAuthoritySelected,
            onDismiss = { selectedItem ->
                if (selectedItem != uiState.revenueAuthoritySelected && selectedItem != null) {
                    onEvent(TaxPaymentInfoViewEvent.UpdateSelectedRevenueAuthority(selectedItem))
                } else {
                    onEvent(
                        TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
                            TaxPaymentFormField.REV_AUTH_CODE,
                            false
                        )
                    )
                }
            }
        )
    }

    if (uiState.showRevenueAccountBottomSheet && uiState.listRevenueAccount != null) {
        RevenueAccountSearchDialog(
            listData = uiState.listRevenueAccount,
            itemSelected = uiState.revenueAccountSelected,
            onDismiss = { selectedItem ->
                if (selectedItem != uiState.revenueAccountSelected && selectedItem != null) {
                    onEvent(TaxPaymentInfoViewEvent.UpdateSelectedRevenueAccount(selectedItem))
                } else {
                    onEvent(
                        TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
                            TaxPaymentFormField.REV_ACC_CODE,
                            false
                        )
                    )
                }
            }
        )
    }

    if (uiState.showAdministrativeAreaBottomSheet && uiState.listAdministrativeArea != null) {
        AdministrativeAreaSearchDialog(
            listData = uiState.listAdministrativeArea,
            itemSelected = uiState.administrativeAreaSelected,
            onDismiss = { selectedItem ->
                if (selectedItem != uiState.administrativeAreaSelected && selectedItem != null) {
                    onEvent(TaxPaymentInfoViewEvent.UpdateSelectedAdministrativeArea(selectedItem))
                } else {
                    onEvent(
                        TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
                            TaxPaymentFormField.ADM_AREA_CODE,
                            false
                        )
                    )
                }
            }
        )
    }

    if (uiState.showTaxPayerEntityBottomSheet) {
        TaxPayerEntityDialog(
            itemSelected = uiState.taxPayerEntitySelected,
            onDismiss = { selectedItem ->
                if (selectedItem != uiState.taxPayerEntitySelected && selectedItem != null) {
                    onEvent(TaxPaymentInfoViewEvent.UpdateSelectedTaxPayerEntity(selectedItem))
                } else {
                    onEvent(
                        TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
                            TaxPaymentFormField.TAX_PAYER_ENTITY,
                            false
                        )
                    )
                }
            }
        )
    }

    if (uiState.showDebitAccountBottomSheet && uiState.listDebitAccounts != null) {
        DebitAccountPickerDialog(
            uiState.listDebitAccounts,
            selected = uiState.debitAccountSelected
        ) {
            if (it != uiState.debitAccountSelected && it != null) {
                onEvent(TaxPaymentInfoViewEvent.UpdateSelectedDebitAccount(it))
            } else {
                onEvent(
                    TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
                        TaxPaymentFormField.DEBIT_ACCOUNT,
                        false
                    )
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewTaxPaymentInfoContent() {
    TaxPaymentInfoContent(
        uiState = TaxPaymentInfoUiState(),
        onEvent = {},
        navHostController = rememberNavController(),
        onDeleteClick = {},
        onEditClick = {}
    )
}