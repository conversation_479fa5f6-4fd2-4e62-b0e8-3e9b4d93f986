package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2

import com.google.gson.JsonPrimitive
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import vn.com.bidv.feature.common.utils.CommonErrCodeConstants
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnTaxItemDto
import vn.com.bidv.feature.government.service.data.governmentservice.model.ValidateCustomsDutyReq
import vn.com.bidv.feature.government.service.domain.model.BalanceAccountDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDetailDMO
import vn.com.bidv.feature.government.service.domain.usecase.GetDetailDebitAccountUseCase
import vn.com.bidv.feature.government.service.domain.usecase.GetFinancialAccountsUseCase
import vn.com.bidv.feature.government.service.domain.usecase.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.domain.usecase.ValidateTxnFormUseCase
import vn.com.bidv.feature.government.service.model.TaxPayerEntity
import vn.com.bidv.feature.government.service.model.TaxPaymentApiCall
import vn.com.bidv.network.NetworkStatusCode
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import javax.inject.Inject
import vn.com.bidv.localization.R as RString

@HiltViewModel
class TaxPaymentInfoViewModel @Inject constructor(
    private val governmentServiceUseCase: GovernmentServiceUseCase,
    private val getFinancialAccountsUseCase: GetFinancialAccountsUseCase,
    private val getDetailDebitAccountUseCase: GetDetailDebitAccountUseCase,
    private val validateTxnFormUseCase: ValidateTxnFormUseCase,
) : ViewModelIBankBase<
        TaxPaymentInfoUiState,
        TaxPaymentInfoViewEvent,
        TaxPaymentInfoSideEffect
        >(
    initialState = TaxPaymentInfoUiState(),
    reducer = TaxPaymentInfoReducer()
) {
    override fun handleEffect(
        sideEffect: TaxPaymentInfoSideEffect,
        onResult: (TaxPaymentInfoViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is TaxPaymentInfoSideEffect.ValidateClientSideSuccess,
            is TaxPaymentInfoSideEffect.RetryValidateServerSide -> {
                callApiValidate(uiState.value, onResult)
            }

            is TaxPaymentInfoSideEffect.GetListDebitAccount -> {
                handleGetListDebitAccount(onResult, sideEffect.shouldShowBottomSheet)
            }

            is TaxPaymentInfoSideEffect.GetListTreasury -> {
                handleGetListTreasury(onResult)
            }

            is TaxPaymentInfoSideEffect.GetListRevenueAccount -> {
                handleGetListRevenueAccount(onResult)
            }

            is TaxPaymentInfoSideEffect.GetListRevenueAuthority -> {
                handleGetListRevenueAuthority(sideEffect.treasury, onResult)
            }

            is TaxPaymentInfoSideEffect.GetListAdministrativeArea -> {
                handleGetListAdministrativeArea(onResult)
            }

            is TaxPaymentInfoSideEffect.InitScreen -> {
                handleInitScreen(
                    sideEffect.treasury,
                    sideEffect.shouldFetchTreasuryDetail,
                    onResult
                )
            }

            is TaxPaymentInfoSideEffect.GetTreasuryDetail -> {
                handleGetTreasuryDetail(sideEffect.treasury, onResult)
            }

            is TaxPaymentInfoSideEffect.CheckAndSetDebitAccount -> {
                handleCheckAndSetDebitAccount(sideEffect.debitAccount, onResult)
            }

            else -> {
                /*Handle nothing*/
            }
        }
    }

    private fun handleCheckAndSetDebitAccount(
        debitAccount: BalanceAccountDMO,
        onResult: (TaxPaymentInfoViewEvent) -> Unit
    ) {
        callDomain(
            isListenAllError = true,
            onSuccess = { result ->
                onResult(
                    TaxPaymentInfoViewEvent.UpdateSelectedDebitAccount(
                        result.data
                    )
                )
            },
            onFail = {
                handleCallCheckDebitAccountValidityFailed(it, onResult)
            }
        ) {
            getDetailDebitAccountUseCase.invoke(debitAccount.accountNo.orEmpty())
        }
    }

    private fun handleInitScreen(
        treasury: TreasuryDMO?,
        shouldFetchTreasuryDetail: Boolean,
        onResult: (TaxPaymentInfoViewEvent) -> Unit,
    ) {
        callDomain(
            isListenAllError = true,
            showLoadingIndicator = true,
            onSuccess = { result ->
                onResult(
                    TaxPaymentInfoViewEvent.UpdateDebitAccountList(
                        result.data?.debitAccounts ?: listOf(),
                        false
                    )
                )
                result.data?.treasuryDetail?.let { treasuryDetail ->
                    onResult(TaxPaymentInfoViewEvent.UpdateBenBankInfo(treasuryDetail))
                }
            },
            onFail = { result ->
                val taxPaymentApiCallAsString = result?.data?.asString
                val taxPaymentApiCall = TaxPaymentApiCall.entries
                    .find { it.name == taxPaymentApiCallAsString }
                    ?: TaxPaymentApiCall.UNSPECIFIED

                if (taxPaymentApiCall == TaxPaymentApiCall.LIST_DEBIT_ACCOUNT) {
                    handleCallGetDebitAccountsFailed(
                        result,
                        onResult
                    )
                }

                onResult(
                    TaxPaymentInfoViewEvent.OnCallApiError(
                        errorCode = result?.errorCode,
                        errorMessage = result?.errorMessage,
                        type = taxPaymentApiCall
                    )
                )
            }
        ) {
            val getDebitAccountsDeferred = async {
                getFinancialAccountsUseCase.invoke()
            }

            val treasuryDetailDeferred = if (shouldFetchTreasuryDetail && treasury != null) {
                async { governmentServiceUseCase.getTreasuryDetail(treasury.treasuryCode.orEmpty()) }
            } else null

            val debitAccountsResult = getDebitAccountsDeferred.await()
            val treasuryDetailResult = treasuryDetailDeferred?.await()



            if (debitAccountsResult is DomainResult.Success && (treasuryDetailResult == null || treasuryDetailResult is DomainResult.Success)) {
                DomainResult.Success(
                    FetchInitDataResult(
                        debitAccounts = debitAccountsResult.data?.items ?: listOf(),
                        treasuryDetail = treasuryDetailResult?.getSafeData()
                    )
                )
            } else {
                val result = when {
                    debitAccountsResult is DomainResult.Error ->
                        DomainResult.Error(
                            errorCode = debitAccountsResult.errorCode,
                            errorMessage = debitAccountsResult.errorMessage,
                            data = JsonPrimitive(TaxPaymentApiCall.LIST_DEBIT_ACCOUNT.name)
                        )
                    treasuryDetailResult is DomainResult.Error ->
                        DomainResult.Error(
                            errorCode = treasuryDetailResult.errorCode,
                            errorMessage = treasuryDetailResult.errorMessage,
                            data = JsonPrimitive(TaxPaymentApiCall.BEN_BANK_NAME.name)
                        )
                    else ->
                        DomainResult.Error(
                            errorCode = "",
                            errorMessage = resourceProvider.getString(
                                RString.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai
                            )
                        )
                }
                result
            }

        }
    }

    private fun callApiValidate(
        uiState: TaxPaymentInfoUiState,
        onEvent: (TaxPaymentInfoViewEvent) -> Unit
    ) {
        callDomain(
            isListenAllError = true,
            onSuccess = { result ->
                result.data?.let {
                    onEvent(TaxPaymentInfoViewEvent.ValidateServerSideSuccess(it))
                } ?: run {
                    onEvent(
                        TaxPaymentInfoViewEvent.OnCallApiError(
                            errorCode = null,
                            errorMessage = resourceProvider.getString(RString.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai),
                            type = TaxPaymentApiCall.VALIDATE_CUSTOMS_DUTY
                        )
                    )
                }
            },
            onFail = {
                onEvent(
                    TaxPaymentInfoViewEvent.OnCallApiError(
                        errorCode = it?.errorCode,
                        errorMessage = it?.errorMessage,
                        type = TaxPaymentApiCall.VALIDATE_CUSTOMS_DUTY
                    )
                )
            }
        ) {
            validateTxnFormUseCase.invoke(
                ValidateCustomsDutyReq(
                    amount = uiState.listPayment.sumOf { it.amount.toLong() }.toString(),
                    ccy = uiState.listPayment.firstOrNull()?.ccy
                        ?: SdkBaseConstants.MoneyCurrencyConstants.VND,
                    treasuryCode = uiState.treasurySelected?.treasuryCode ?: "",
                    admAreaCode = uiState.administrativeAreaSelected?.admAreaCode ?: "",
                    revAuthCode = uiState.revenueAuthoritySelected?.revAuthCode ?: "",
                    revAccCode = uiState.revenueAccountSelected?.revAccCode ?: "",
                    debitAccNo = uiState.debitAccountSelected?.accountNo ?: "",
                    taxCode = uiState.taxPayerInfo?.taxId ?: "",
                    payerName = uiState.taxPayerInfo?.name ?: "",
                    payerAddr = uiState.taxPayerInfo?.address ?: "",
                    altTaxCode = uiState.taxDelegatorInfo?.taxId,
                    altPayerName = uiState.taxDelegatorInfo?.name,
                    altPayerAddr = uiState.taxDelegatorInfo?.address,
                    payerType = if (uiState.taxPayerEntitySelected == TaxPayerEntity.BUSINESS) ValidateCustomsDutyReq.PayerType.BUSINESS else ValidateCustomsDutyReq.PayerType.INDIVIDUAL,
                    taxItems = uiState.listPayment.map {
                        TxnTaxItemDto(
                            eiTypeCode = it.eiTypeCode,
                            taxTypeCode = it.taxTypeCode,
                            ccCode = it.ccCode,
                            chapterCode = it.chapterCode,
                            ecCode = it.ecCode,
                            amount = it.amount,
                            ccy = it.ccy,
                            declarationDate = it.declarationDate,
                            declarationNo = it.declarationNo,
                            transDesc = it.transDesc
                        )
                    },
                    txnId = uiState.editingTransactionId,
                    raNote = uiState.noteToApprover,
                    priority = uiState.priorityTransaction,
                    orgId = uiState.customerTransactionCode,
                )
            )
        }
    }

    private fun handleGetListDebitAccount(
        onEvent: (TaxPaymentInfoViewEvent) -> Unit,
        shouldShowBottomSheet: Boolean
    ) {
        callDomain(
            isListenAllError = true,
            onSuccess = {
                it.data?.items?.let { accounts ->
                    onEvent(
                        TaxPaymentInfoViewEvent.UpdateDebitAccountList(
                            accounts,
                            shouldShowBottomSheet
                        )
                    )
                } ?: run {
                    onEvent(
                        TaxPaymentInfoViewEvent.UpdateDebitAccountList(
                            listOf(),
                            shouldShowBottomSheet
                        )
                    )
                }
            },
            onFail = {
                handleCallGetDebitAccountsFailed(it, onEvent)
            }
        ) {
            getFinancialAccountsUseCase.invoke()
        }
    }

    private fun handleGetListTreasury(onEvent: (TaxPaymentInfoViewEvent) -> Unit) {
        callDomain(
            isListenAllError = true,
            onSuccess = { result ->
                result.data?.items?.let { treasuries ->
                    onEvent(TaxPaymentInfoViewEvent.UpdateTreasuryList(treasuries))
                } ?: run {
                    onEvent(TaxPaymentInfoViewEvent.UpdateTreasuryList(listOf()))
                }
            },
            onFail = {
                onEvent(
                    TaxPaymentInfoViewEvent.OnCallApiError(
                        errorCode = it?.errorCode,
                        errorMessage = it?.errorMessage,
                        type = TaxPaymentApiCall.TREASURY_CODE
                    )
                )
            }
        ) {
            governmentServiceUseCase.getListTreasury()
        }
    }

    private fun handleGetTreasuryDetail(
        treasury: TreasuryDMO,
        onEvent: (TaxPaymentInfoViewEvent) -> Unit
    ) {
        callDomain(
            isListenAllError = true,
            onSuccess = { result ->
                result.data?.let { treasury ->
                    onEvent(TaxPaymentInfoViewEvent.UpdateBenBankInfo(treasury))
                } ?: run {
                    onEvent(
                        TaxPaymentInfoViewEvent.OnCallApiError(
                            errorCode = null,
                            errorMessage = resourceProvider.getString(RString.string.co_loi_xay_ra_trong_qua_trinh_xu_ly_vui_long_thu_lai),
                            type = TaxPaymentApiCall.BEN_BANK_NAME
                        )
                    )
                }
            },
            onFail = {
                onEvent(
                    TaxPaymentInfoViewEvent.OnCallApiError(
                        errorCode = it?.errorCode,
                        errorMessage = it?.errorMessage,
                        type = TaxPaymentApiCall.BEN_BANK_NAME
                    )
                )
            }
        ) {
            governmentServiceUseCase.getTreasuryDetail(treasury.treasuryCode.orEmpty())
        }
    }

    private fun handleGetListRevenueAccount(onEvent: (TaxPaymentInfoViewEvent) -> Unit) {
        callDomain(
            isListenAllError = true,
            onSuccess = { result ->
                result.data?.items?.let {
                    onEvent(TaxPaymentInfoViewEvent.UpdateRevenueAccountList(it))
                } ?: run {
                    onEvent(TaxPaymentInfoViewEvent.UpdateRevenueAccountList(listOf()))
                }
            },
            onFail = {
                onEvent(
                    TaxPaymentInfoViewEvent.OnCallApiError(
                        errorCode = it?.errorCode,
                        errorMessage = it?.errorMessage,
                        type = TaxPaymentApiCall.REV_ACC_CODE
                    )
                )
            }
        ) {
            governmentServiceUseCase.getListRevenueAccount()
        }
    }

    private fun handleGetListRevenueAuthority(
        treasury: TreasuryDMO,
        onEvent: (TaxPaymentInfoViewEvent) -> Unit
    ) {
        callDomain(
            isListenAllError = true,
            onSuccess = { result ->
                result.data?.items?.let {
                    onEvent(TaxPaymentInfoViewEvent.UpdateRevenueAuthorityList(it))
                } ?: run {
                    onEvent(TaxPaymentInfoViewEvent.UpdateRevenueAuthorityList(listOf()))
                }
            },
            onFail = {
                onEvent(
                    TaxPaymentInfoViewEvent.OnCallApiError(
                        errorCode = it?.errorCode,
                        errorMessage = it?.errorMessage,
                        type = TaxPaymentApiCall.REV_AUTH_CODE
                    )
                )
            }
        ) {
            governmentServiceUseCase.getListRevenueAuthority(treasury.treasuryCode ?: "")
        }
    }

    private fun handleGetListAdministrativeArea(onEvent: (TaxPaymentInfoViewEvent) -> Unit) {
        callDomain(
            isListenAllError = true,
            onSuccess = { result ->
                result.data?.items?.let {
                    onEvent(TaxPaymentInfoViewEvent.UpdateAdministrativeAreaList(it))
                } ?: run {
                    onEvent(TaxPaymentInfoViewEvent.UpdateAdministrativeAreaList(listOf()))
                }
            },
            onFail = {
                onEvent(
                    TaxPaymentInfoViewEvent.OnCallApiError(
                        errorCode = it?.errorCode,
                        errorMessage = it?.errorMessage,
                        type = TaxPaymentApiCall.ADM_AREA_CODE
                    )
                )
            }
        ) {
            governmentServiceUseCase.getListAdministrativeArea(null)
        }
    }


    private fun handleCallGetDebitAccountsFailed(error: DomainResult.Error?, onEvent: (TaxPaymentInfoViewEvent) -> Unit) {
        val errorMessage = when (error?.errorCode) {
            NetworkStatusCode.CONNECT_TIME_OUT -> resourceProvider.getString(
                RString.string.lay_danh_sach_tai_khoan_trich_no_khong_thanh_cong_vui_long_thu_lai
            )

            CommonErrCodeConstants.MD0003 -> resourceProvider.getString(
                RString.string.khong_co_tai_khoan_hop_le_de_thuc_hien_giao_dich
            )

            else -> error?.errorMessage
        }
        onEvent(
            TaxPaymentInfoViewEvent.OnCallApiError(
                errorCode = error?.errorCode,
                errorMessage = errorMessage,
                type = TaxPaymentApiCall.LIST_DEBIT_ACCOUNT
            )
        )
    }

    private fun handleCallCheckDebitAccountValidityFailed(error: DomainResult.Error?, onEvent: (TaxPaymentInfoViewEvent) -> Unit) {
        val errorMessage = when (error?.errorCode) {
            NetworkStatusCode.CONNECT_TIME_OUT -> resourceProvider.getString(
                RString.string.lay_chi_tiet_tai_khoan_khong_thanh_cong_vui_long_thu_lai
            )

            else -> error?.errorMessage
        }
        onEvent(
            TaxPaymentInfoViewEvent.OnCallApiError(
                errorCode = error?.errorCode,
                errorMessage = errorMessage,
                type = TaxPaymentApiCall.DETAIL_DEBIT_ACCOUNT
            )
        )
    }

    data class FetchInitDataResult(
        val debitAccounts: List<BalanceAccountDMO>,
        val treasuryDetail: TreasuryDetailDMO?
    )
}