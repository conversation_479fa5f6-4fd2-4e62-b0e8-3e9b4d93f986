package vn.com.bidv.feature.government.service.domain.model

import com.google.gson.annotations.SerializedName
import vn.com.bidv.feature.government.service.model.TxnInitPushType

data class TxnInitPushDMO (

    /* <PERSON>ại đ<PERSON> */
    @SerializedName("type")
    val type: TxnInitPushType,

    /* Key giao dịch */
    @SerializedName("transKey")
    val transKey: String? = null,

    /* Mã giao dịch */
    @SerializedName("txnIds")
    val txnIds: List<String>? = null

)