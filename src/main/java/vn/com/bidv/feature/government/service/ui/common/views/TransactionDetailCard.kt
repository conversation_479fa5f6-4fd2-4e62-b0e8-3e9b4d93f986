package vn.com.bidv.feature.government.service.ui.common.views

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.IBankSectionHeader
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.LeadingType
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBSpacing.spacing2xs
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.government.service.model.FeeMethod
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun TransactionDetailCard(
    transactionData: TransactionDetailDMO,
    showOrgId: Boolean = false,
    showNoteToAuthorizer: Boolean = false,
) {
    InfoCard(
        title = stringResource(R.string.chi_tiet_giao_dich),
        showPriority = transactionData.priority == true,
    ) {
        val listItemDetailFieldLabelValue = listOf(
            R.string.ma_so_thue_nguoi_nop_thue to transactionData.payerTaxCode,
            R.string.ho_ten_nguoi_nop_thue to transactionData.payerName,
            R.string.dia_chi_nguoi_nop_thue to transactionData.payerAddress,
            R.string.ma_so_nguoi_nop_thay to transactionData.altTaxCode,
            R.string.ho_ten_nguoi_nop_thay to transactionData.altPayerName,
            R.string.dia_chi_nguoi_nop_thay to transactionData.altPayerAddress,
            R.string.dia_ban_hanh_chinh to formatCodeName(transactionData.admAreaCode, transactionData.authorityName),
            R.string.ngan_hang_thu_huong to formatCodeName(transactionData.benBankCode, transactionData.benBankName),
            R.string.ma_giao_dich_khach_hang to transactionData.orgId.takeIf { showOrgId },
            R.string.loai_hinh_nnt to transactionData.payerTypeName,
            R.string.phi_dich_vu_bao_gom_vat to when (transactionData.feeMethod) {
                FeeMethod.CONT_FEE.serializedName -> stringResource(R.string.khach_hang_dang_ky_phi_khoan)
                else -> transactionData.feeTotal.formatMoney(
                    transactionData.feeCcy,
                    isShowCurrCode = true
                )
            },
            R.string.ghi_chu_den_nguoi_duyet to transactionData.noteToAuthorizer.takeIf { showNoteToAuthorizer },

        )
        listItemDetailFieldLabelValue.forEach { (labelRes, dataValue) ->
            when {
                labelRes in listOf(
                    R.string.ma_so_nguoi_nop_thay,
                    R.string.ho_ten_nguoi_nop_thay,
                    R.string.dia_chi_nguoi_nop_thay
                ) && dataValue == null || dataValue.isNullOrEmpty() -> return@forEach

                else -> DetailItem(
                    label = stringResource(labelRes),
                    value = dataValue
                )
            }
        }
    }
}


@Composable
fun InfoCard(
    title: String,
    showPriority: Boolean = false,
    body: @Composable () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = LocalColorScheme.current.bgMainTertiary,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
            )
    ) {
        IBankSectionHeader(
            shLeadingType = LeadingType.Dash(),
            shSectionTitle = title,
        ) {
            if (showPriority) {
                IBankBadgeLabel(
                    title = stringResource(R.string.uu_tien),
                    badgeSize = LabelSize.M,
                    badgeColor = LabelColor.BRAND
                )
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = IBSpacing.spacingM)

        ) {
            body()
        }
    }
}

@Composable
fun DetailItem(label: String, value: String) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = spacingXs),
        verticalArrangement = Arrangement.spacedBy(spacing2xs, Alignment.Top),
        horizontalAlignment = Alignment.Start,
    ) {
        Text(
            text = label,
            style = curLocalTypography.bodyBody_m,
            color = curLocalColorScheme.contentMainTertiary
        )

        Text(
            text = value,
            style = curLocalTypography.titleTitle_m,
            color = curLocalColorScheme.contentMainPrimary,
            fontWeight = FontWeight.Medium
        )
    }
}

@Preview(showBackground = true, name = "InfoCard")
@Composable
fun PreviewInfoCard() {
    InfoCard("title") {
        listOf(1,2,3,4).forEach { item ->
            DetailItem("title $item", "value: $item")
        }
    }
}

@Preview(showBackground = true, name = "DetailItem")
@Composable
fun PreviewDetailItem() {
    DetailItem("title", "value")
}

@Preview(showBackground = true, name = "Transaction Detail Card Preview")
@Composable
fun PreviewTransactionDetailCard() {
    val mockData = TransactionDetailDMO(
        transactionId = "TX123456789",
        payerName = "Công ty đầu tư và phát triển AA",
        payerAddress = "Bạch Mai, Hai Bà Trưng, Hà Nội",
        admAreaName = "Địa bàn Hai Bà Trưng",
        paymentAmount = "1550000",
        altPayerName = "",
        paymentAmountText = "Một tỷ năm trăm năm mươi triệu đồng",
        createdDate = "08/05/2025 10:30:45",
        status = "REJECTED",
        statusName = "Từ chối duyệt",
        authorityName = "Cơ quan hành chính Hà Nội",
        payerType = 1,
        noteToAuthorizer = "Nộp thuế doanh nghiệp hàng tháng",
        tccRefNo = "TX-2025-TX123456789",
        payerTaxCode = "*************",
        feeTotal = "15000000",
        taxItems = emptyList(),
        denialReasonNote = "",
        ccy = "VND"
    )
    TransactionDetailCard(transactionData = mockData)
}

