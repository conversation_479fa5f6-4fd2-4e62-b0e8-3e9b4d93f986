package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.detail

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.utils.unpackV2
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBSpacing.spacingM
import vn.com.bidv.designsystem.theme.IBSpacing.spacingS
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.navigation.NavigationHelper.navigateToActionHistoryScreen
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.model.DownloadDocumentArguments
import vn.com.bidv.feature.government.service.domain.model.TransactionApprovalContent
import vn.com.bidv.feature.government.service.model.CustomFeeInfoMainRouteArgument
import vn.com.bidv.feature.government.service.model.DeleteTransactionFlowPopupArguments
import vn.com.bidv.feature.government.service.model.GetTransactionDetailPurpose
import vn.com.bidv.feature.government.service.model.RootScreen
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO
import vn.com.bidv.feature.government.service.model.TxnInitPushType
import vn.com.bidv.feature.government.service.model.UserType
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute
import vn.com.bidv.feature.government.service.ui.common.views.ActionBarContent
import vn.com.bidv.feature.government.service.ui.common.views.AllTransGeneralInfoSection
import vn.com.bidv.feature.government.service.ui.common.views.AmountSection
import vn.com.bidv.feature.government.service.ui.common.views.BankCardSection
import vn.com.bidv.feature.government.service.ui.common.views.GeneralInfoSection
import vn.com.bidv.feature.government.service.ui.common.views.TaxPaymentSection
import vn.com.bidv.feature.government.service.ui.common.views.TransactionDetailCard
import vn.com.bidv.feature.government.service.util.TransactionMenuActions
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun TransactionDetailScreen(
    navController: NavHostController,
    transactionId: String,
    rootScreen: RootScreen
) {
    val isAllTransactionScreen = rootScreen == RootScreen.AllList

    val viewModel: TransactionDetailViewModel = hiltViewModel()
    val (transDetailUiState, transDetailOnEvent, _) = viewModel.unpackV2()

    var menuExpanded by remember { mutableStateOf(false) }
    val (topMenuActions, bottomButtonAction) = TransactionMenuActions.getMenuActions(rootScreen)

    val invokeAction =
        { action: ActionType ->
            when (action) {
                ActionType.EDIT -> {
                    if (transDetailUiState.value.transactionData != null) {
                        navController.navigate(
                            GovernmentServiceRoute.CustomsFeeInfoMainRoute.createRoute(
                                CustomFeeInfoMainRouteArgument(
                                    createMode = GetTransactionDetailPurpose.EDIT_TRANSACTION,
                                    transactionDetailDMO = transDetailUiState.value.transactionData
                                )
                            )
                        )
                    }
                }

                ActionType.Copy_Transaction -> {
                    if (transDetailUiState.value.transactionData != null) {
                        navController.navigate(
                            GovernmentServiceRoute.CustomsFeeInfoMainRoute.createRoute(
                                CustomFeeInfoMainRouteArgument(
                                    createMode = GetTransactionDetailPurpose.COPY_TRANSACTION,
                                    transactionDetailDMO = transDetailUiState.value.transactionData,
                                )
                            )
                        )
                    }
                }

                ActionType.Print_Document -> {
                    navController.navigate(
                        GovernmentServiceRoute.DownloadDocumentRoute(
                            DownloadDocumentArguments(
                                downloadDocumentType = DownloadDocumentArguments.DownloadDocumentType.PRINT_DOCUMENT,
                                txnIds = listOf(transDetailUiState.value.transactionId)
                            )
                        )
                    )
                }

                ActionType.History_Impact -> {
                    navigateToActionHistoryScreen(
                        navController,
                        transactionId,
                        Constants.GOV_TRANS_CODE
                    )
                }

                ActionType.Delete -> {
                    navController.navigate(
                        GovernmentServiceRoute.DeleteTransactionFlowPopupRoute(
                            DeleteTransactionFlowPopupArguments(
                                txnIds = listOf(transactionId),
                                rootScreen = RootScreen.Detail,
                            )
                        )
                    )
                }

                ActionType.Push_Approval -> {
                    navController.navigate(
                        GovernmentServiceRoute.BaseTransactionApprovalRoute(
                            TransactionApprovalContent(
                                isShowPopupConfirm = true,
                                txnIds = listOf(transactionId),
                                userType = UserType.MAKER,
                                type = TxnInitPushType.PUSH
                            )
                        )
                    )
                }

                ActionType.Update_Status_Transaction -> {
                    transDetailOnEvent(
                        TransactionDetailReducer.TransactionDetailViewEvent.UpdateStatusTransaction(
                            transactionId
                        )
                    )
                }

                else -> { /* do nothing with the rest */
                }
            }
        }

    val topAppBarConfig = TopAppBarConfig(
        titleTopAppBar = stringResource(vn.com.bidv.localization.R.string.chi_tiet_giao_dich),
        actionItems = {
            ActionBarContent(
                menuExpanded,
                { menuExpanded = it },
                topMenuActions,
                invokeAction
            )
        }
    )

    BaseScreen(
        navController = navController,
        viewModel = viewModel,
        topAppBarConfig = topAppBarConfig
    ) { uiState, onEvent ->

        LaunchedEffect(key1 = true) {
            onEvent(
                TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail(
                    transactionId
                )
            )
        }

        uiState.transactionData?.let { transactionData ->
            TransactionDetailContent(
                navController,
                transactionData,
                isAllTransactionScreen,
                bottomButtonAction
            ) { invokeAction(it) }
        }
    }
}

@Composable
private fun TransactionDetailContent(
    navController: NavHostController,
    data: TransactionDetailDMO,
    isAllTransactionScreen: Boolean,
    bottomButtonAction: ActionType?,
    onInvokeAction: (ActionType) -> Unit,
) {
    val curLocalColorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState())
                .padding(
                    bottom = spacingM,
                    start = spacingM,
                    end = spacingM
                ),
            verticalArrangement = Arrangement.spacedBy(spacingM)
        ) {
            if (isAllTransactionScreen)
                AllTransGeneralInfoSection(data)
            BankCardSection(data)
            AmountSection(
                amount = data.paymentAmount.formatMoney(
                    data.ccy,
                    isShowCurrCode = true
                ),
                amountText = "${data.paymentAmountText}"
            )
            TransactionDetailCard(transactionData = data)
            TaxPaymentSection(items = data.taxItems ?: emptyList()) {
                navController.navigate(
                    GovernmentServiceRoute.TaxPaymentDetailRoute(
                        args = it
                    )
                )
            }
            if (!isAllTransactionScreen) {
                GeneralInfoSection(data)
            }
        }

        val showShowBottomAction = !isAllTransactionScreen || (
                data.customsConnStatus != TransactionStatusBase.SUCCESS.statusCode
                        && data.status == TransactionStatusBase.SUCCESS.statusCode
                )
        if (showShowBottomAction && bottomButtonAction != null) {
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(curLocalColorScheme.bgMainTertiary)
                    .padding(horizontal = spacingM, vertical = spacingS),
            ) {
                IBankNormalButton(
                    modifier = Modifier.fillMaxWidth(),
                    type = NormalButtonType.PRIMARY(curLocalColorScheme),
                    text = stringResource(bottomButtonAction.titleRes)
                ) {
                    onInvokeAction.invoke(bottomButtonAction)
                }
            }
        }
    }

}