package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import vn.com.bidv.feature.government.service.ui.common.CommonDropDown
import vn.com.bidv.feature.government.service.ui.common.CommonSearchDialog
import vn.com.bidv.feature.government.service.domain.model.ExportImportTypeDMO
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentReducer
import vn.com.bidv.feature.government.service.util.GovernmentServiceUtils.formatCodeName
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
fun TradeTypeDropDown(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
) {
    CommonDropDown(
        labelText = stringResource(R.string.loai_hinh_xnk),
        selectedItem = uiState.tradeTypeSelected,
        displayTextSelector = {
            formatCodeName(it?.eiTypeCode, it?.eiTypeName)
        },
        fieldError = uiState.fieldError[AddPaymentItem.DropDownItem.TradeType],
        onClickEnd = {
            onEvent(AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.ShowTradeTypeBottomSheet(true))
        },
        onClickClear = {
            onEvent(AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.ClearTradeType)
        }
    )
}

@Composable
fun ShowTradeTypeBottomSheet(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    onValueChange: (AddPaymentItem.DropDownItem.TradeType, ExportImportTypeDMO?) -> Unit
) {
    if (uiState.showTradeTypeBottomSheet) {
        CommonSearchDialog(
            title = stringResource(R.string.loai_hinh_xnk),
            itemSelected = uiState.tradeTypeSelected,
            listData = uiState.listTradeType,
            showSearchBox = true,
            searchFilter = { item, query ->
                val search = "${item.eiTypeCode}${item.eiTypeName}"
                VNCharacterUtil.removeAccent(search)
                    .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
            },
            onDismiss = {
                onEvent(AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.ShowTradeTypeBottomSheet(false))
                onValueChange(AddPaymentItem.DropDownItem.TradeType, it)
            },
            itemTitleSelector = { formatCodeName(it.eiTypeCode, it.eiTypeName) },
            compareKey = { a, b -> a.eiTypeCode == b?.eiTypeCode }
        )
    }
}
