package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step2

import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.government.service.domain.model.AdministrativeAreaDMO
import vn.com.bidv.feature.government.service.domain.model.BalanceAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAuthorityDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO
import vn.com.bidv.feature.government.service.domain.model.ValidateTxnDMO
import vn.com.bidv.feature.government.service.model.CustomsFeeInfoStep2Content
import vn.com.bidv.feature.government.service.model.TaxPayerEntity
import vn.com.bidv.feature.government.service.model.TaxPayerInfo
import vn.com.bidv.feature.government.service.model.TaxPaymentApiCall
import vn.com.bidv.feature.government.service.model.TaxPaymentFormField
import vn.com.bidv.feature.government.service.model.TaxPaymentFormFieldValidateError
import vn.com.bidv.feature.government.service.model.TaxPaymentValidateFailedPopupType

class TaxPaymentInfoReducerTest {

    private lateinit var reducer: TaxPaymentInfoReducer

    private val initialState = TaxPaymentInfoUiState()

    private val mockDebitAccount =
        BalanceAccountDMO("1", "Debit Account", "1000", "VND", default = true)
    private val mockTreasury = TreasuryDMO("T01", "Treasury 1", "A01", "Area 1")
    private val mockRevenueAuthority = RevenueAuthorityDMO("RA01", "Revenue Authority 1")
    private val mockRevenueAccount = RevenueAccountDMO("RAC01", "Revenue Account 1")
    private val mockAdminArea = AdministrativeAreaDMO("AA01", "Admin Area 1")

    private val completePayment = TaxPaymentDMO(
        declarationNo = "D1",
        declarationDate = "2023-01-01",
        chapterCode = "C1",
        ecCode = "E1",
        amount = "1000",
        ccy = "VND",
        transDesc = "desc",
        taxTypeCode = "T1",
        ccCode = "CC1",
        eiTypeCode = "EI1",
        treasuryCode = "T01",
        treasuryName = "Treasury 1",
        revAuthCode = "RA01",
        revAuthName = "Revenue Authority 1",
        revAccCode = "RAC01",
        revAccName = "Revenue Account 1",
        admAreaCode = "A01",
        admAreaName = "Area 1",
        payerType = 1,
        eiTypeName = null,
        taxTypeName = null,
        ccName = null,
        chapterName = null,
        ecName = null,
    )

    @Before
    fun setUp() {
        reducer = TaxPaymentInfoReducer()
    }

    @Test
    fun `InitScreen with empty payment list`() {
        val event = TaxPaymentInfoViewEvent.InitScreen(
            CustomsFeeInfoStep2Content(
                listPayment = listOf(),
                taxPayerInfo = TaxPayerInfo("", "", ""),
                taxDelegatorInfo = TaxPayerInfo("", "", ""),
                noteToApprover = null,
                priorityTransaction = null,
                customerTransactionCode = null,
            )
        )
        val (state, effect) = reducer.reduce(initialState, event)
        assertTrue(state.isInitialized)
        assertTrue(state.listPayment.isEmpty())
        assertNull(state.treasurySelected)
    }

    @Test
    fun `InitScreen with valid payment list`() {
        val payments = listOf(completePayment)
        val event = TaxPaymentInfoViewEvent.InitScreen(
            CustomsFeeInfoStep2Content(
                listPayment = payments,
                taxPayerInfo = TaxPayerInfo("", "", ""),
                taxDelegatorInfo = TaxPayerInfo("", "", ""),
                noteToApprover = null,
                priorityTransaction = null,
                customerTransactionCode = null,
            )
        )
        val (state, effect) = reducer.reduce(initialState, event)
        assertTrue(state.isInitialized)
        assertEquals(1, state.listPayment.size)
        assertNotNull(state.treasurySelected)
        assertEquals("T01", state.treasurySelected?.treasuryCode)
    }

    @Test
    fun `InitScreen with payments having null fields`() {
        val paymentWithNulls = completePayment.copy(treasuryCode = null)
        val event = TaxPaymentInfoViewEvent.InitScreen(
            CustomsFeeInfoStep2Content(
                listPayment = listOf(paymentWithNulls),
                taxPayerInfo = TaxPayerInfo("", "", ""),
                taxDelegatorInfo = TaxPayerInfo("", "", ""),
                noteToApprover = null,
                priorityTransaction = null,
                customerTransactionCode = null,
            )
        )
        val (state, effect) = reducer.reduce(initialState, event)
        assertTrue(state.isInitialized)
        assertNull(state.treasurySelected)
    }

    @Test
    fun `RetryInit for all field types`() {
        TaxPaymentApiCall.entries.forEach { field ->
            if (field == TaxPaymentApiCall.VALIDATE_CUSTOMS_DUTY) return@forEach
            val event = TaxPaymentInfoViewEvent.RetryFetchData(field)
            val (_, effect) = reducer.reduce(initialState, event)
            if (field == TaxPaymentApiCall.REV_AUTH_CODE) {
                assertNull(effect)
            }
        }
    }

    @Test
    fun `UpdateDebitAccountList with a default account`() {
        val accounts =
            listOf(mockDebitAccount, mockDebitAccount.copy(default = false, accountNo = "2"))
        val event = TaxPaymentInfoViewEvent.UpdateDebitAccountList(accounts, true)
        val (state, effect) = reducer.reduce(initialState, event)
        assertEquals(accounts, state.listDebitAccounts)
        assertEquals(mockDebitAccount, state.debitAccountSelected)
        assertEquals(null, effect)
    }

    @Test
    fun `UpdateDebitAccountList without a default account`() {
        val accounts = listOf(
            mockDebitAccount.copy(default = false, accountNo = "2"),
            mockDebitAccount.copy(default = false, accountNo = "3")
        )
        val event = TaxPaymentInfoViewEvent.UpdateDebitAccountList(accounts, true)
        val (state, effect) = reducer.reduce(initialState, event)
        assertEquals(accounts, state.listDebitAccounts)
        assertEquals(accounts.first(), state.debitAccountSelected)
        assertEquals(null, effect)
    }

    @Test
    fun `UpdateTreasuryList`() {
        val treasuries = listOf(mockTreasury)
        val event = TaxPaymentInfoViewEvent.UpdateTreasuryList(treasuries)
        val (state, effect) = reducer.reduce(initialState, event)
        assertEquals(treasuries, state.listTreasury)
        assertEquals(null, effect)
    }

    @Test
    fun `UpdateRevenueAuthorityList`() {
        val authorities = listOf(mockRevenueAuthority)
        val event = TaxPaymentInfoViewEvent.UpdateRevenueAuthorityList(authorities)
        val (state, effect) = reducer.reduce(initialState, event)
        assertEquals(authorities, state.listRevenueAuthority)
        assertEquals(null, effect)
    }

    @Test
    fun `UpdateRevenueAccountList`() {
        val accounts = listOf(mockRevenueAccount)
        val event = TaxPaymentInfoViewEvent.UpdateRevenueAccountList(accounts)
        val (state, effect) = reducer.reduce(initialState, event)
        assertEquals(accounts, state.listRevenueAccount)
        assertEquals(null, effect)
    }

    @Test
    fun `UpdateAdministrativeAreaList`() {
        val areas = listOf(mockAdminArea)
        val event = TaxPaymentInfoViewEvent.UpdateAdministrativeAreaList(areas)
        val (state, effect) = reducer.reduce(initialState, event)
        assertEquals(areas, state.listAdministrativeArea)
        assertEquals(null, effect)
    }

    @Test
    fun `UpdateSelectedDebitAccount`() {
        val event = TaxPaymentInfoViewEvent.UpdateSelectedDebitAccount(mockDebitAccount)
        val (state, effect) = reducer.reduce(
            initialState.copy(showDebitAccountBottomSheet = true),
            event
        )
        assertEquals(mockDebitAccount, state.debitAccountSelected)
        assertFalse(state.showDebitAccountBottomSheet)
        assertNull(effect)
    }

    @Test
    fun `UpdateSelectedTreasury`() {
        val event = TaxPaymentInfoViewEvent.UpdateSelectedTreasury(mockTreasury)
        val (state, effect) = reducer.reduce(
            initialState.copy(showTreasuryBottomSheet = true),
            event
        )
        assertEquals(mockTreasury, state.treasurySelected)
        assertNotNull(state.administrativeAreaSelected)
        assertEquals(mockTreasury.admAreaCode, state.administrativeAreaSelected?.admAreaCode)
        assertFalse(state.showTreasuryBottomSheet)
        assertNull(state.listRevenueAuthority)
        assertNull(state.revenueAuthoritySelected)
        assertNull(state.listAdministrativeArea)
    }

    @Test
    fun `UpdateSelectedRevenueAuthority`() {
        val event = TaxPaymentInfoViewEvent.UpdateSelectedRevenueAuthority(mockRevenueAuthority)
        val (state, effect) = reducer.reduce(
            initialState.copy(showRevenueAuthorityBottomSheet = true),
            event
        )
        assertEquals(mockRevenueAuthority, state.revenueAuthoritySelected)
        assertFalse(state.showRevenueAuthorityBottomSheet)
        assertNull(effect)
    }

    @Test
    fun `UpdateSelectedRevenueAccount`() {
        val event = TaxPaymentInfoViewEvent.UpdateSelectedRevenueAccount(mockRevenueAccount)
        val (state, effect) = reducer.reduce(
            initialState.copy(showRevenueAccountBottomSheet = true),
            event
        )
        assertEquals(mockRevenueAccount, state.revenueAccountSelected)
        assertFalse(state.showRevenueAccountBottomSheet)
        assertNull(effect)
    }

    @Test
    fun `UpdateSelectedAdministrativeArea`() {
        val event = TaxPaymentInfoViewEvent.UpdateSelectedAdministrativeArea(mockAdminArea)
        val (state, effect) = reducer.reduce(
            initialState.copy(showAdministrativeAreaBottomSheet = true),
            event
        )
        assertEquals(mockAdminArea, state.administrativeAreaSelected)
        assertFalse(state.showAdministrativeAreaBottomSheet)
        assertNull(effect)
    }

    @Test
    fun `UpdateNoteToApproverString`() {
        val note = "Test Note"
        val event = TaxPaymentInfoViewEvent.UpdateNoteToApproverString(note)
        val (state, effect) = reducer.reduce(initialState, event)
        assertEquals(note, state.noteToApprover)
        assertNull(effect)
    }

    @Test
    fun `UpdateSelectedTaxPayerEntity`() {
        val entity = TaxPayerEntity.INDIVIDUAL
        val event = TaxPaymentInfoViewEvent.UpdateSelectedTaxPayerEntity(entity)
        val (state, effect) = reducer.reduce(
            initialState.copy(showTaxPayerEntityBottomSheet = true),
            event
        )
        assertEquals(entity, state.taxPayerEntitySelected)
        assertFalse(state.showTaxPayerEntityBottomSheet)
        assertNull(effect)
    }

    @Test
    fun `OnCallApiError`() {
        val event =
            TaxPaymentInfoViewEvent.OnCallApiError("E01", "Error", TaxPaymentApiCall.LIST_DEBIT_ACCOUNT)
        val (state, effect) = reducer.reduce(initialState, event)
        assertEquals(initialState, state)
        assertEquals(
            TaxPaymentInfoSideEffect.OnCallApiError(
                "E01",
                "Error",
                TaxPaymentApiCall.LIST_DEBIT_ACCOUNT
            ), effect
        )
    }

    @Test
    fun `ValidateBeforeContinue with all fields filled`() {
        val stateWithData = initialState.copy(
            debitAccountSelected = mockDebitAccount,
            treasurySelected = mockTreasury,
            revenueAuthoritySelected = mockRevenueAuthority,
            revenueAccountSelected = mockRevenueAccount,
            administrativeAreaSelected = mockAdminArea,
            listPayment = listOf(completePayment)
        )
        val event = TaxPaymentInfoViewEvent.ValidateBeforeContinue
        val (state, effect) = reducer.reduce(stateWithData, event)
        assertNull(state.formError)
        assertEquals(TaxPaymentInfoSideEffect.ValidateClientSideSuccess, effect)
    }

    @Test
    fun `ValidateBeforeContinue with missing debit account`() {
        val stateWithMissingData = initialState.copy(
            treasurySelected = mockTreasury,
            revenueAuthoritySelected = mockRevenueAuthority,
            revenueAccountSelected = mockRevenueAccount,
            administrativeAreaSelected = mockAdminArea,
            listPayment = listOf(completePayment)
        )
        val event = TaxPaymentInfoViewEvent.ValidateBeforeContinue
        val (state, effect) = reducer.reduce(stateWithMissingData, event)
        assertNotNull(state.formError)
        assertEquals(null, effect)
    }

    @Test
    fun `ValidateBeforeContinue with missing treasury`() {
        val stateWithMissingData = initialState.copy(
            debitAccountSelected = mockDebitAccount,
            revenueAuthoritySelected = mockRevenueAuthority,
            revenueAccountSelected = mockRevenueAccount,
            administrativeAreaSelected = mockAdminArea,
            listPayment = listOf(completePayment)
        )
        val event = TaxPaymentInfoViewEvent.ValidateBeforeContinue
        val (state, effect) = reducer.reduce(stateWithMissingData, event)
        assertEquals(TaxPaymentFormFieldValidateError.RequiredFieldNotFilled, state.formError)
        assertEquals(null, effect)
    }

    @Test
    fun `ValidateBeforeContinue with missing revenue authority`() {
        val stateWithMissingData = initialState.copy(
            debitAccountSelected = mockDebitAccount,
            treasurySelected = mockTreasury,
            revenueAccountSelected = mockRevenueAccount,
            administrativeAreaSelected = mockAdminArea,
            listPayment = listOf(completePayment)
        )
        val event = TaxPaymentInfoViewEvent.ValidateBeforeContinue
        val (state, effect) = reducer.reduce(stateWithMissingData, event)
        assertEquals(TaxPaymentFormFieldValidateError.RequiredFieldNotFilled, state.formError)
        assertEquals(null, effect)
    }

    @Test
    fun `ValidateBeforeContinue with missing revenue account`() {
        val stateWithMissingData = initialState.copy(
            debitAccountSelected = mockDebitAccount,
            treasurySelected = mockTreasury,
            revenueAuthoritySelected = mockRevenueAuthority,
            administrativeAreaSelected = mockAdminArea,
            listPayment = listOf(completePayment)
        )
        val event = TaxPaymentInfoViewEvent.ValidateBeforeContinue
        val (state, effect) = reducer.reduce(stateWithMissingData, event)
        assertEquals(TaxPaymentFormFieldValidateError.RequiredFieldNotFilled, state.formError)
        assertEquals(null, effect)
    }

    @Test
    fun `ValidateBeforeContinue with missing administrative area`() {
        val stateWithMissingData = initialState.copy(
            debitAccountSelected = mockDebitAccount,
            treasurySelected = mockTreasury,
            revenueAuthoritySelected = mockRevenueAuthority,
            revenueAccountSelected = mockRevenueAccount,
            listPayment = listOf(completePayment)
        )
        val event = TaxPaymentInfoViewEvent.ValidateBeforeContinue
        val (state, effect) = reducer.reduce(stateWithMissingData, event)
        assertEquals(TaxPaymentFormFieldValidateError.RequiredFieldNotFilled, state.formError)
        assertEquals(null, effect)
    }

    @Test
    fun `ValidateBeforeContinue with empty payment list`() {
        val stateWithData = initialState.copy(
            debitAccountSelected = mockDebitAccount,
            treasurySelected = mockTreasury,
            revenueAuthoritySelected = mockRevenueAuthority,
            revenueAccountSelected = mockRevenueAccount,
            administrativeAreaSelected = mockAdminArea,
            listPayment = emptyList()
        )
        val event = TaxPaymentInfoViewEvent.ValidateBeforeContinue
        val (state, effect) = reducer.reduce(stateWithData, event)
        assertNull(state.formError)
        assertEquals(TaxPaymentInfoSideEffect.ValidateClientSideSuccess, effect)
    }

    @Test
    fun `ValidateBeforeContinue with incomplete payment fields`() {
        val incompletePayment = completePayment.copy(declarationNo = "")
        val stateWithData = initialState.copy(
            debitAccountSelected = mockDebitAccount,
            treasurySelected = mockTreasury,
            revenueAuthoritySelected = mockRevenueAuthority,
            revenueAccountSelected = mockRevenueAccount,
            administrativeAreaSelected = mockAdminArea,
            listPayment = listOf(incompletePayment)
        )
        val event = TaxPaymentInfoViewEvent.ValidateBeforeContinue
        val (state, effect) = reducer.reduce(stateWithData, event)
        assertEquals(
            TaxPaymentFormFieldValidateError.PaymentsFieldIsNotCompleted(incompletePayment),
            state.formError
        )
        assertEquals(
            TaxPaymentInfoSideEffect.ValidateBeforeContinueFailed(
                TaxPaymentValidateFailedPopupType.PAYMENTS_NOT_FILLED
            ), effect
        )
    }

    @Test
    fun `test ToggleBottomSheetVisibility`() {
        // Test each field type with show=true and show=false

        // DEBIT_ACCOUNT field
        var event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.DEBIT_ACCOUNT,
            true
        )
        var (state, effect) = reducer.reduce(initialState, event)
        assertEquals(
            TaxPaymentInfoSideEffect.GetListDebitAccount(true),
            effect
        )

        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.DEBIT_ACCOUNT,
            false
        )
        var stateWithSheetOpen = initialState.copy(showDebitAccountBottomSheet = true)
        var result = reducer.reduce(stateWithSheetOpen, event)
        state = result.first
        effect = result.second
        assertFalse(state.showDebitAccountBottomSheet)
        assertNull(effect)

        // TREASURY_CODE field - with null list (should trigger GetListTreasury effect)
        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.TREASURY_CODE,
            true
        )
        result = reducer.reduce(initialState, event)
        state = result.first
        effect = result.second
        assertEquals(TaxPaymentInfoSideEffect.GetListTreasury, effect)

        // TREASURY_CODE field - with existing list
        stateWithSheetOpen = initialState.copy(
            listTreasury = listOf(mockTreasury),
            showTreasuryBottomSheet = true
        )
        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.TREASURY_CODE,
            false
        )
        result = reducer.reduce(stateWithSheetOpen, event)
        state = result.first
        effect = result.second
        assertFalse(state.showTreasuryBottomSheet)
        assertNull(effect)

        // REV_AUTH_CODE field - with null list and selected treasury (should trigger GetListRevenueAuthority effect)
        val stateWithTreasury = initialState.copy(treasurySelected = mockTreasury)
        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.REV_AUTH_CODE,
            true
        )
        result = reducer.reduce(stateWithTreasury, event)
        state = result.first
        effect = result.second
        assertEquals(TaxPaymentInfoSideEffect.GetListRevenueAuthority(mockTreasury), effect)

        // REV_AUTH_CODE field - with existing list
        stateWithSheetOpen = initialState.copy(
            listRevenueAuthority = listOf(mockRevenueAuthority),
            showRevenueAuthorityBottomSheet = true
        )
        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.REV_AUTH_CODE,
            false
        )
        result = reducer.reduce(stateWithSheetOpen, event)
        state = result.first
        effect = result.second
        assertFalse(state.showRevenueAuthorityBottomSheet)
        assertNull(effect)

        // REV_ACC_CODE field - with null list (should trigger GetListRevenueAccount effect)
        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.REV_ACC_CODE,
            true
        )
        result = reducer.reduce(initialState, event)
        state = result.first
        effect = result.second
        assertEquals(TaxPaymentInfoSideEffect.GetListRevenueAccount, effect)

        // REV_ACC_CODE field - with existing list
        stateWithSheetOpen = initialState.copy(
            listRevenueAccount = listOf(mockRevenueAccount),
            showRevenueAccountBottomSheet = true
        )
        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.REV_ACC_CODE,
            false
        )
        result = reducer.reduce(stateWithSheetOpen, event)
        state = result.first
        effect = result.second
        assertFalse(state.showRevenueAccountBottomSheet)
        assertNull(effect)

        // ADM_AREA_CODE field - with null list (should trigger GetListAdministrativeArea effect)
        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.ADM_AREA_CODE,
            true
        )
        result = reducer.reduce(initialState, event)
        state = result.first
        effect = result.second
        assertEquals(TaxPaymentInfoSideEffect.GetListAdministrativeArea, effect)

        // ADM_AREA_CODE field - with existing list
        stateWithSheetOpen = initialState.copy(
            listAdministrativeArea = listOf(mockAdminArea),
            showAdministrativeAreaBottomSheet = true
        )
        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.ADM_AREA_CODE,
            false
        )
        result = reducer.reduce(stateWithSheetOpen, event)
        state = result.first
        effect = result.second
        assertFalse(state.showAdministrativeAreaBottomSheet)
        assertNull(effect)

        // TAX_PAYER_ENTITY field
        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.TAX_PAYER_ENTITY,
            true
        )
        result = reducer.reduce(initialState, event)
        state = result.first
        effect = result.second
        assertTrue(state.showTaxPayerEntityBottomSheet)
        assertNull(effect)

        stateWithSheetOpen = initialState.copy(showTaxPayerEntityBottomSheet = true)
        event = TaxPaymentInfoViewEvent.ToggleBottomSheetVisibility(
            TaxPaymentFormField.TAX_PAYER_ENTITY,
            false
        )
        result = reducer.reduce(stateWithSheetOpen, event)
        state = result.first
        effect = result.second
        assertFalse(state.showTaxPayerEntityBottomSheet)
        assertNull(effect)
    }

    @Test
    fun `test DeletePayment`() {
        // Case 1: Multiple payments - deleting one should not set shouldPopToPreviousStep to true
        val payment1 = completePayment.copy(uniqueId = "1")
        val payment2 = completePayment.copy(uniqueId = "2")
        val stateWithMultiplePayments = initialState.copy(listPayment = listOf(payment1, payment2))

        val event = TaxPaymentInfoViewEvent.DeletePayment(payment1)
        val (state, effect) = reducer.reduce(stateWithMultiplePayments, event)

        assertEquals(1, state.listPayment.size)
        assertEquals("2", state.listPayment[0].uniqueId)
        assertTrue(effect is TaxPaymentInfoSideEffect.DeletePaymentSuccess)
        assertFalse((effect as TaxPaymentInfoSideEffect.DeletePaymentSuccess).shouldPopToPreviousStep)

        // Case 2: Single payment - deleting it should set shouldPopToPreviousStep to true
        val stateWithSinglePayment = initialState.copy(listPayment = listOf(payment1))
        val (stateAfterDelete, effectAfterDelete) = reducer.reduce(stateWithSinglePayment, event)

        assertTrue(stateAfterDelete.listPayment.isEmpty())
        assertTrue(effectAfterDelete is TaxPaymentInfoSideEffect.DeletePaymentSuccess)
        assertTrue((effectAfterDelete as TaxPaymentInfoSideEffect.DeletePaymentSuccess).shouldPopToPreviousStep)
    }

    @Test
    fun `test ValidateServerSideSuccess`() {
        // Case 1: All required fields are present
        val completeState = initialState.copy(
            taxPayerInfo = TaxPayerInfo("123", "Test User", "Test Address"),
            taxDelegatorInfo = TaxPayerInfo("456", "Delegate", "Delegate Address"),
            listPayment = listOf(completePayment),
            debitAccountSelected = mockDebitAccount,
            treasurySelected = mockTreasury,
            revenueAuthoritySelected = mockRevenueAuthority,
            revenueAccountSelected = mockRevenueAccount,
            administrativeAreaSelected = mockAdminArea,
            noteToApprover = "Test note"
        )

        val validateResult = ValidateTxnDMO()
        val event = TaxPaymentInfoViewEvent.ValidateServerSideSuccess(validateResult)
        val (_, effect) = reducer.reduce(completeState, event)

        assertNotNull(effect)
        assertTrue(effect is TaxPaymentInfoSideEffect.ValidateServerSideSuccess)
        val content =
            (effect as TaxPaymentInfoSideEffect.ValidateServerSideSuccess).customsFeeInfoStep3Content

        assertEquals(completeState.taxPayerInfo, content.taxPayerInfo)
        assertEquals(completeState.taxDelegatorInfo, content.taxDelegatorInfo)
        assertEquals(completeState.listPayment.map {
            it.copy(
                admAreaName = completeState.administrativeAreaSelected?.admAreaName,
                admAreaCode = completeState.administrativeAreaSelected?.admAreaCode
            )
        }, content.listPayment)
        assertEquals(completeState.debitAccountSelected, content.debitAccount)
        assertEquals(completeState.treasurySelected, content.treasury)
        assertEquals(completeState.revenueAuthoritySelected, content.revenueAuthority)
        assertEquals(completeState.revenueAccountSelected, content.revenueAccount)
        assertEquals(completeState.administrativeAreaSelected, content.administrativeArea)
        assertEquals(completeState.taxPayerEntitySelected, content.taxPayerEntity)
        assertEquals(completeState.noteToApprover, content.noteToApprover)
        assertEquals(validateResult, content.validateResult)

        // Case 2: Missing required fields should return null effect
        val incompleteState = initialState.copy(
            taxPayerInfo = null,
            debitAccountSelected = mockDebitAccount,
            treasurySelected = mockTreasury
        )

        val (_, nullEffect) = reducer.reduce(incompleteState, event)
        assertNull(nullEffect)
    }

    @Test
    fun `test ClearData`() {
        // Setup a state with various fields populated
        val populatedState = initialState.copy(
            isInitialized = true,
            taxPayerInfo = TaxPayerInfo("123", "Test User", "Test Address"),
            taxDelegatorInfo = TaxPayerInfo("456", "Delegate", "Delegate Address"),
            listDebitAccounts = listOf(mockDebitAccount),
            listPayment = listOf(completePayment),
            listTreasury = listOf(mockTreasury),
            listRevenueAuthority = listOf(mockRevenueAuthority),
            listRevenueAccount = listOf(mockRevenueAccount),
            listAdministrativeArea = listOf(mockAdminArea),
            debitAccountSelected = mockDebitAccount,
            treasurySelected = mockTreasury,
            revenueAuthoritySelected = mockRevenueAuthority,
            revenueAccountSelected = mockRevenueAccount,
            administrativeAreaSelected = mockAdminArea,
            showDebitAccountBottomSheet = true,
            noteToApprover = "Test note",
            priorityTransaction = true,
            customerTransactionCode = "CUST123",
            formError = TaxPaymentFormFieldValidateError.RequiredFieldNotFilled
        )

        val event = TaxPaymentInfoViewEvent.ClearData
        val (clearedState, effect) = reducer.reduce(populatedState, event)

        // Verify all fields are reset to initial values
        assertFalse(clearedState.isInitialized)
        assertNull(clearedState.taxPayerInfo)
        assertNull(clearedState.taxDelegatorInfo)
        assertNull(clearedState.listDebitAccounts)
        assertTrue(clearedState.listPayment.isEmpty())
        assertNull(clearedState.listTreasury)
        assertNull(clearedState.listRevenueAuthority)
        assertNull(clearedState.listRevenueAccount)
        assertNull(clearedState.listAdministrativeArea)
        assertNull(clearedState.debitAccountSelected)
        assertNull(clearedState.treasurySelected)
        assertNull(clearedState.revenueAuthoritySelected)
        assertNull(clearedState.revenueAccountSelected)
        assertNull(clearedState.administrativeAreaSelected)
        assertFalse(clearedState.showDebitAccountBottomSheet)
        assertEquals("", clearedState.noteToApprover)
        assertFalse(clearedState.priorityTransaction)
        assertEquals("", clearedState.customerTransactionCode)
        assertNull(clearedState.formError)
        assertNull(effect)
    }

    @Test
    fun `test UpdatePaymentItem`() {
        // Create multiple payments
        val payment1 = completePayment.copy(uniqueId = "1", declarationNo = "D1", amount = "1000")
        val payment2 = completePayment.copy(uniqueId = "2", declarationNo = "D2", amount = "2000")
        val stateWithPayments = initialState.copy(listPayment = listOf(payment1, payment2))

        // Update one payment
        val updatedPayment = payment1.copy(
            declarationNo = "D1-Updated",
            amount = "1500",
            transDesc = "Updated description"
        )

        val event = TaxPaymentInfoViewEvent.UpdatePaymentItem(updatedPayment)
        val (state, effect) = reducer.reduce(stateWithPayments, event)

        // Verify the payment was updated correctly
        assertEquals(2, state.listPayment.size)

        val firstPayment = state.listPayment.find { it.uniqueId == "1" }
        assertNotNull(firstPayment)
        assertEquals("D1-Updated", firstPayment?.declarationNo)
        assertEquals("1500", firstPayment?.amount)
        assertEquals("Updated description", firstPayment?.transDesc)

        // Verify the other payment remains unchanged
        val secondPayment = state.listPayment.find { it.uniqueId == "2" }
        assertNotNull(secondPayment)
        assertEquals("D2", secondPayment?.declarationNo)
        assertEquals("2000", secondPayment?.amount)

        assertNull(effect)
    }

    @Test
    fun `test TogglePriorityTransaction`() {
        // Test toggling from false to true
        val event = TaxPaymentInfoViewEvent.TogglePriorityTransaction(true)
        val (state, effect) = reducer.reduce(initialState, event)

        assertTrue(state.priorityTransaction)
        assertNull(effect)

        // Test toggling from true to false
        val stateWithPriorityEnabled = initialState.copy(priorityTransaction = true)
        val toggleOffEvent = TaxPaymentInfoViewEvent.TogglePriorityTransaction(false)
        val (updatedState, updatedEffect) = reducer.reduce(stateWithPriorityEnabled, toggleOffEvent)

        assertFalse(updatedState.priorityTransaction)
        assertNull(updatedEffect)
    }

    @Test
    fun `test UpdateCustomerTransactionCode`() {
        val transactionCode = "CUST-12345"
        val event = TaxPaymentInfoViewEvent.UpdateCustomerTransactionCode(transactionCode)
        val (state, effect) = reducer.reduce(initialState, event)

        assertEquals(transactionCode, state.customerTransactionCode)
        assertNull(effect)

        // Test updating an existing code
        val stateWithExistingCode = initialState.copy(customerTransactionCode = "OLD-CODE")
        val (updatedState, updatedEffect) = reducer.reduce(stateWithExistingCode, event)

        assertEquals(transactionCode, updatedState.customerTransactionCode)
        assertNull(updatedEffect)
    }
}
