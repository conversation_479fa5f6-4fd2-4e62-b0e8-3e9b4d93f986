package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.detail

import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO

class TransactionDetailReducerTest {

    private lateinit var reducer: TransactionDetailReducer
    private lateinit var initialState: TransactionDetailReducer.TransactionDetailViewState

    @Before
    fun setUp() {
        reducer = TransactionDetailReducer()
        initialState = TransactionDetailReducer.TransactionDetailViewState()
    }

    // MARK: - Initial State Tests
    @Test
    fun `test initial state default values`() {
        assertEquals("", initialState.transactionId)
        assertNull(initialState.transactionData)
    }

    @Test
    fun `test TransactionDetailViewState data class properties`() {
        val testTransactionId = "TXN123"
        val testTransactionData = createTestTransactionDetailDMO()
        val state = TransactionDetailReducer.TransactionDetailViewState(
            transactionId = testTransactionId,
            transactionData = testTransactionData
        )
        
        assertEquals(testTransactionId, state.transactionId)
        assertEquals(testTransactionData, state.transactionData)
        assertTrue(state is ViewState)
    }

    @Test
    fun `test TransactionDetailViewState should implement ViewState interface`() {
        assertTrue(initialState is ViewState)
    }

    @Test
    fun `test reducer should implement Reducer interface`() {
        assertTrue(reducer is Reducer<*, *, *>)
    }

    // MARK: - GetTransactionDetail Event Tests
    @Test
    fun `test GetTransactionDetail event with valid transaction ID`() {
        // Arrange
        val transactionId = "TXN123"
        val event = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail(transactionId)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState) // State should remain unchanged
        assertNotNull(effect)
        assertTrue(effect is TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail)
        val fetchEffect = effect as TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail
        assertEquals(transactionId, fetchEffect.transactionId)
    }

    @Test
    fun `test GetTransactionDetail event with empty transaction ID`() {
        // Arrange
        val event = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail("")

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState)
        assertNotNull(effect)
        assertTrue(effect is TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail)
        val fetchEffect = effect as TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail
        assertEquals("", fetchEffect.transactionId)
    }

    @Test
    fun `test GetTransactionDetail event with long transaction ID`() {
        // Arrange
        val longTransactionId = "TXN" + "0".repeat(100)
        val event = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail(longTransactionId)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState)
        assertNotNull(effect)
        assertTrue(effect is TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail)
        val fetchEffect = effect as TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail
        assertEquals(longTransactionId, fetchEffect.transactionId)
    }

    @Test
    fun `test GetTransactionDetail event with special characters in transaction ID`() {
        // Arrange
        val specialTransactionId = "TXN-123_456@789"
        val event = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail(specialTransactionId)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(initialState, newState)
        assertNotNull(effect)
        assertTrue(effect is TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail)
        val fetchEffect = effect as TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail
        assertEquals(specialTransactionId, fetchEffect.transactionId)
    }

    // MARK: - FillTransactionDetail Event Tests
    @Test
    fun `test FillTransactionDetail event with valid transaction data`() {
        // Arrange
        val testTransactionData = createTestTransactionDetailDMO()
        val event = TransactionDetailReducer.TransactionDetailViewEvent.FillTransactionDetail(testTransactionData)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertEquals(testTransactionData, newState.transactionData)
        assertEquals(initialState.transactionId, newState.transactionId) // Other fields unchanged
        assertNull(effect) // No side effect should be produced
    }

    @Test
    fun `test FillTransactionDetail event with null transaction data`() {
        // Arrange
        val event = TransactionDetailReducer.TransactionDetailViewEvent.FillTransactionDetail(null)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertNull(newState.transactionData)
        assertEquals(initialState.transactionId, newState.transactionId)
        assertNull(effect)
    }

    @Test
    fun `test FillTransactionDetail event with transaction data containing null optional fields`() {
        // Arrange
        val testTransactionData = createTestTransactionDetailDMO(
            debitAccNumber = null,
            debitAccName = null,
            payerTaxCode = null,
            payerName = null,
            payerAddress = null
        )
        val event = TransactionDetailReducer.TransactionDetailViewEvent.FillTransactionDetail(testTransactionData)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertNotNull(newState.transactionData)
        assertNull(newState.transactionData?.debitAccNumber)
        assertNull(newState.transactionData?.debitAccName)
        assertNull(newState.transactionData?.payerTaxCode)
        assertNull(newState.transactionData?.payerName)
        assertNull(newState.transactionData?.payerAddress)
        assertNull(effect)
    }

    @Test
    fun `test FillTransactionDetail event with complete transaction data`() {
        // Arrange
        val testTransactionData = createTestTransactionDetailDMO(
            transactionId = "TXN123",
            debitAccNumber = "*********0",
            debitAccName = "Test Account",
            payerTaxCode = "*********",
            payerName = "Test Company",
            payerAddress = "123 Test Street",
            paymentAmount = "1000000",
            ccy = "VND"
        )
        val event = TransactionDetailReducer.TransactionDetailViewEvent.FillTransactionDetail(testTransactionData)

        // Act
        val (newState, effect) = reducer.reduce(initialState, event)

        // Assert
        assertNotNull(newState.transactionData)
        assertEquals("TXN123", newState.transactionData?.transactionId)
        assertEquals("*********0", newState.transactionData?.debitAccNumber)
        assertEquals("Test Account", newState.transactionData?.debitAccName)
        assertEquals("*********", newState.transactionData?.payerTaxCode)
        assertEquals("Test Company", newState.transactionData?.payerName)
        assertEquals("123 Test Street", newState.transactionData?.payerAddress)
        assertEquals("1000000", newState.transactionData?.paymentAmount)
        assertEquals("VND", newState.transactionData?.ccy)
        assertNull(effect)
    }

    // MARK: - State Immutability Tests
    @Test
    fun `test state immutability when reduce is called multiple times`() {
        // Arrange
        val firstTransactionData = createTestTransactionDetailDMO(transactionId = "FIRST")
        val firstEvent = TransactionDetailReducer.TransactionDetailViewEvent.FillTransactionDetail(firstTransactionData)
        
        val secondTransactionData = createTestTransactionDetailDMO(transactionId = "SECOND")
        val secondEvent = TransactionDetailReducer.TransactionDetailViewEvent.FillTransactionDetail(secondTransactionData)

        // Act
        val (firstState, _) = reducer.reduce(initialState, firstEvent)
        val (secondState, _) = reducer.reduce(firstState, secondEvent)

        // Assert
        assertEquals("FIRST", firstState.transactionData?.transactionId)
        assertEquals("SECOND", secondState.transactionData?.transactionId)
        assertTrue(firstState !== secondState) // Different instances
    }

    // MARK: - Event Hierarchy Tests
    @Test
    fun `test TransactionDetailViewEvent sealed class hierarchy`() {
        val getDetailEvent = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail("TXN123")
        assertTrue(getDetailEvent is TransactionDetailReducer.TransactionDetailViewEvent)
        assertTrue(getDetailEvent is ViewEvent)
        
        val fillDetailEvent = TransactionDetailReducer.TransactionDetailViewEvent.FillTransactionDetail(createTestTransactionDetailDMO())
        assertTrue(fillDetailEvent is TransactionDetailReducer.TransactionDetailViewEvent)
        assertTrue(fillDetailEvent is ViewEvent)
    }

    @Test
    fun `test TransactionDetailEffect sealed class hierarchy`() {
        val fetchEffect = TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail("TXN123")
        assertTrue(fetchEffect is TransactionDetailReducer.TransactionDetailEffect)
        assertTrue(fetchEffect is SideEffect)
    }

    // MARK: - Reducer Behavior Tests
    @Test
    fun `test reducer should be stateless and reusable`() {
        // Arrange
        val anotherReducer = TransactionDetailReducer()
        val event = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail("TXN123")

        // Act
        val (result1, effect1) = reducer.reduce(initialState, event)
        val (result2, effect2) = anotherReducer.reduce(initialState, event)

        // Assert
        assertEquals(result1, result2)
        assertEquals(effect1, effect2)
    }

    @Test
    fun `test reducer with sequential events`() {
        // Arrange
        val getDetailEvent = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail("TXN123")
        val transactionData = createTestTransactionDetailDMO(transactionId = "TXN123")
        val fillDetailEvent = TransactionDetailReducer.TransactionDetailViewEvent.FillTransactionDetail(transactionData)

        // Act
        val (state1, effect1) = reducer.reduce(initialState, getDetailEvent)
        val (state2, effect2) = reducer.reduce(state1, fillDetailEvent)

        // Assert
        assertEquals(initialState, state1) // State unchanged after GetTransactionDetail
        assertNotNull(effect1)
        assertTrue(effect1 is TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail)
        
        assertEquals(transactionData, state2.transactionData) // State updated after FillTransactionDetail
        assertNull(effect2)
    }

    @Test
    fun `test reducer with multiple GetTransactionDetail events`() {
        // Arrange
        val event1 = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail("TXN1")
        val event2 = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail("TXN2")

        // Act
        val (state1, effect1) = reducer.reduce(initialState, event1)
        val (state2, effect2) = reducer.reduce(state1, event2)

        // Assert
        assertEquals(initialState, state1)
        assertEquals(initialState, state2)
        assertNotNull(effect1)
        assertNotNull(effect2)
        assertTrue(effect1 is TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail)
        assertTrue(effect2 is TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail)
        assertEquals("TXN1", (effect1 as TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail).transactionId)
        assertEquals("TXN2", (effect2 as TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail).transactionId)
    }

    @Test
    fun `test reducer with multiple FillTransactionDetail events`() {
        // Arrange
        val data1 = createTestTransactionDetailDMO(transactionId = "TXN1")
        val data2 = createTestTransactionDetailDMO(transactionId = "TXN2")
        val event1 = TransactionDetailReducer.TransactionDetailViewEvent.FillTransactionDetail(data1)
        val event2 = TransactionDetailReducer.TransactionDetailViewEvent.FillTransactionDetail(data2)

        // Act
        val (state1, effect1) = reducer.reduce(initialState, event1)
        val (state2, effect2) = reducer.reduce(state1, event2)

        // Assert
        assertEquals(data1, state1.transactionData)
        assertEquals(data2, state2.transactionData)
        assertNull(effect1)
        assertNull(effect2)
    }

    // MARK: - Data Class Functionality Tests
    @Test
    fun `test data class should support equality`() {
        // Arrange
        val state1 = TransactionDetailReducer.TransactionDetailViewState(
            transactionId = "TXN123",
            transactionData = createTestTransactionDetailDMO()
        )
        val state2 = TransactionDetailReducer.TransactionDetailViewState(
            transactionId = "TXN123",
            transactionData = createTestTransactionDetailDMO()
        )
        val state3 = TransactionDetailReducer.TransactionDetailViewState(
            transactionId = "TXN456",
            transactionData = createTestTransactionDetailDMO()
        )

        // Act & Assert
        assertEquals(state1, state2)
        assertTrue(state1 != state3)
    }

    @Test
    fun `test data class should support copy functionality`() {
        // Arrange
        val originalState = TransactionDetailReducer.TransactionDetailViewState(
            transactionId = "ORIGINAL",
            transactionData = createTestTransactionDetailDMO()
        )

        // Act
        val copiedState = originalState.copy(transactionId = "COPIED")

        // Assert
        assertEquals("ORIGINAL", originalState.transactionId)
        assertEquals("COPIED", copiedState.transactionId)
        assertEquals(originalState.transactionData, copiedState.transactionData)
        assertTrue(originalState !== copiedState)
    }

    @Test
    fun `test event data class should support equality`() {
        // Arrange
        val event1 = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail("TXN123")
        val event2 = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail("TXN123")
        val event3 = TransactionDetailReducer.TransactionDetailViewEvent.GetTransactionDetail("TXN456")

        // Act & Assert
        assertEquals(event1, event2)
        assertTrue(event1 != event3)
    }

    @Test
    fun `test effect data class should support equality`() {
        // Arrange
        val effect1 = TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail("TXN123")
        val effect2 = TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail("TXN123")
        val effect3 = TransactionDetailReducer.TransactionDetailEffect.FetchTransactionDetail("TXN456")

        // Act & Assert
        assertEquals(effect1, effect2)
        assertTrue(effect1 != effect3)
    }

    // MARK: - Helper Methods
    private fun createTestTransactionDetailDMO(
        transactionId: String = "TXN123",
        debitAccNumber: String? = "*********0",
        debitAccName: String? = "Test Account",
        payerTaxCode: String? = "*********",
        payerName: String? = "Test Company",
        payerAddress: String? = "123 Test Street",
        paymentAmount: String? = "1000000",
        ccy: String? = "VND"
    ): TransactionDetailDMO {
        return TransactionDetailDMO(
            transactionId = transactionId,
            debitAccNumber = debitAccNumber,
            debitAccName = debitAccName,
            payerTaxCode = payerTaxCode,
            payerName = payerName,
            payerAddress = payerAddress,
            paymentAmount = paymentAmount,
            ccy = ccy
        )
    }
} 