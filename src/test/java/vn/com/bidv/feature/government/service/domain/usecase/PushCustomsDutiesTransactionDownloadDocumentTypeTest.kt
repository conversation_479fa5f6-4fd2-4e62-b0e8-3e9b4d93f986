package vn.com.bidv.feature.government.service.domain.usecase

import com.google.gson.Gson
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.every
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnConfirmReq
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnInitPushReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnInitPushRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnProcessResultRes
import vn.com.bidv.feature.government.service.data.governmentservice.model.TransactionResDetail
import vn.com.bidv.feature.government.service.domain.model.TxnProcessResultDMO
import vn.com.bidv.feature.government.service.domain.model.TxnInitPushDMO
import vn.com.bidv.feature.government.service.model.TxnInitPushType
import vn.com.bidv.sdkbase.domain.DomainResult

class PushCustomsDutiesTransactionDownloadDocumentTypeTest {

    private lateinit var governmentServiceRepository: GovernmentServiceRepository
    private lateinit var pushCustomsDutiesTransactionUseCase: PushCustomsDutiesTransactionUseCase
    private lateinit var gson: Gson

    @Before
    fun setUp() {
        governmentServiceRepository = mockk()
        pushCustomsDutiesTransactionUseCase = PushCustomsDutiesTransactionUseCase(governmentServiceRepository)
        gson = Gson()
    }

    @Test
    fun `test initTransaction with valid input returns success result`() = runTest {
        val txnIds = listOf("TXN001", "TXN002")
        val input = InputVerifyTransaction(txnIds = txnIds)
        val expectedResponse = TxnInitPushRes(
            transKey = "test_trans_key"
        )
        val expectedResult = NetworkResult.Success(expectedResponse)

        coEvery {
            governmentServiceRepository.initPushTransaction(
                TxnInitPushReq(
                    txnIds = txnIds,
                    type = TxnInitPushReq.Type.PUSH
                )
            )
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.initTransaction(input)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals("test_trans_key", successResult.data?.transKey)
    }

    @Test
    fun `test initTransaction with empty txnIds returns success result`() = runTest {
        val txnIds = emptyList<String>()
        val input = InputVerifyTransaction(txnIds = txnIds)
        val expectedResponse = TxnInitPushRes(
            transKey = "empty_trans_key"
        )
        val expectedResult = NetworkResult.Success(expectedResponse)

        coEvery {
            governmentServiceRepository.initPushTransaction(
                TxnInitPushReq(
                    txnIds = txnIds,
                    type = TxnInitPushReq.Type.PUSH
                )
            )
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.initTransaction(input)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals("empty_trans_key", successResult.data?.transKey)
    }


    @Test
    fun `test initCreateTransaction with PUSH_EDIT type returns success result`() = runTest {
        val txnInitPushDMO = TxnInitPushDMO(
            type = TxnInitPushType.PUSH_EDIT,
            transKey = "edit_trans_key"
        )
        val dataString = gson.toJson(txnInitPushDMO)
        val input = InputVerifyCreateTransaction(dataString = dataString)
        val expectedResponse = TxnInitPushRes(
            transKey = "edit_trans_key"
        )
        val expectedResult = NetworkResult.Success(expectedResponse)

        coEvery {
            governmentServiceRepository.initPushTransaction(
                TxnInitPushReq(
                    transKey = "edit_trans_key",
                    type = TxnInitPushReq.Type.PUSH_EDIT
                )
            )
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.initCreateTransaction(input)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals("edit_trans_key", successResult.data?.transKey)
    }

    @Test
    fun `test initCreateTransaction with PUSH_SAVE type returns success result`() = runTest {
        val txnInitPushDMO = TxnInitPushDMO(
            type = TxnInitPushType.PUSH_SAVE,
            transKey = "save_trans_key"
        )
        val dataString = gson.toJson(txnInitPushDMO)
        val input = InputVerifyCreateTransaction(dataString = dataString)
        val expectedResponse = TxnInitPushRes(
            transKey = "save_trans_key"
        )
        val expectedResult = NetworkResult.Success(expectedResponse)

        coEvery {
            governmentServiceRepository.initPushTransaction(
                TxnInitPushReq(
                    transKey = "save_trans_key",
                    type = TxnInitPushReq.Type.PUSH_SAVE
                )
            )
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.initCreateTransaction(input)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals("save_trans_key", successResult.data?.transKey)
    }

    @Test
    fun `test initCreateTransaction with PUSH type defaults to PUSH_SAVE`() = runTest {
        val txnInitPushDMO = TxnInitPushDMO(
            type = TxnInitPushType.PUSH,
            transKey = "push_trans_key"
        )
        val dataString = gson.toJson(txnInitPushDMO)
        val input = InputVerifyCreateTransaction(dataString = dataString)
        val expectedResponse = TxnInitPushRes(
            transKey = "push_trans_key"
        )
        val expectedResult = NetworkResult.Success(expectedResponse)

        coEvery {
            governmentServiceRepository.initPushTransaction(
                TxnInitPushReq(
                    transKey = "push_trans_key",
                    type = TxnInitPushReq.Type.PUSH_SAVE
                )
            )
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.initCreateTransaction(input)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals("push_trans_key", successResult.data?.transKey)
    }

    @Test
    fun `test initCreateTransaction with null transKey returns success result`() = runTest {
        val txnInitPushDMO = TxnInitPushDMO(
            type = TxnInitPushType.PUSH_SAVE,
            transKey = null
        )
        val dataString = gson.toJson(txnInitPushDMO)
        val input = InputVerifyCreateTransaction(dataString = dataString)
        val expectedResponse = TxnInitPushRes(
            transKey = "generated_key"
        )
        val expectedResult = NetworkResult.Success(expectedResponse)

        coEvery {
            governmentServiceRepository.initPushTransaction(
                TxnInitPushReq(
                    transKey = null,
                    type = TxnInitPushReq.Type.PUSH_SAVE
                )
            )
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.initCreateTransaction(input)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        assertEquals("generated_key", successResult.data?.transKey)
    }

    @Test
    fun `test verifyTransaction with valid input and all fields returns success result`() = runTest {
        val initResponse = InitVerifyTransactionResponse(
            transKey = "verify_trans_key"
        )
        val reqValue = "confirmation_value"
        
        val failTxnDetail = mockk<TransactionResDetail> {
            every { txnId } returns "FAIL001"
            every { message } returns "Transaction failed"
            every { code } returns "ERR001"
        }
        
        val confirmResponse = mockk<TxnProcessResultRes> {
            every { treasuryCode } returns "T001"
            every { admAreaCode } returns "ADM001"
            every { revAccCode } returns "REV001"
            every { revAuthCode } returns "AUTH001"
            every { treasuryName } returns "Treasury Name"
            every { admAreaName } returns "Admin Area Name"
            every { revAccName } returns "Revenue Account Name"
            every { revAuthName } returns "Revenue Authority Name"
            every { failTxns } returns listOf(failTxnDetail)
            every { txnId } returns "TXN001"
            every { totalAmount } returns "1000000"
            every { ccy } returns "VND"
            every { feeTotal } returns "50000"
            every { feeCcy } returns "VND"
            every { feeOpt } returns "DEBIT"
            every { createdDate } returns "2023-12-01"
            every { debitAccNo } returns "*********"
            every { debitAccName } returns "Debit Account Name"
            every { total } returns 10L
            every { totalSuccess } returns 9L
            every { totalFail } returns 1L
            every { totalAmountText } returns "One million dong"
        }
        val expectedResult = NetworkResult.Success(confirmResponse)

        coEvery {
            governmentServiceRepository.confirmPushTransaction(
                TxnConfirmReq(
                    transKey = "verify_trans_key",
                    confirmValue = "confirmation_value"
                )
            )
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.verifyTransaction(initResponse, reqValue)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        val txnProcessResult = gson.fromJson(successResult.data, TxnProcessResultDMO::class.java)
        
        assertEquals("T001", txnProcessResult.treasuryCode)
        assertEquals("ADM001", txnProcessResult.admAreaCode)
        assertEquals("REV001", txnProcessResult.revAccCode)
        assertEquals("AUTH001", txnProcessResult.revAuthCode)
        assertEquals("Treasury Name", txnProcessResult.treasuryName)
        assertEquals("Admin Area Name", txnProcessResult.admAreaName)
        assertEquals("Revenue Account Name", txnProcessResult.revAccName)
        assertEquals("Revenue Authority Name", txnProcessResult.revAuthName)
        assertEquals(1, txnProcessResult.failTxns?.size)
        assertEquals("FAIL001", txnProcessResult.failTxns?.first()?.txnId)
        assertEquals("Transaction failed", txnProcessResult.failTxns?.first()?.message)
        assertEquals("ERR001", txnProcessResult.failTxns?.first()?.code)
        assertEquals("TXN001", txnProcessResult.txnId)
        assertEquals("1000000", txnProcessResult.totalAmount)
        assertEquals("VND", txnProcessResult.ccy)
        assertEquals("50000", txnProcessResult.feeTotal)
        assertEquals("VND", txnProcessResult.feeCcy)
        assertEquals("DEBIT", txnProcessResult.feeOpt)
        assertEquals("2023-12-01", txnProcessResult.createdDate)
        assertEquals("*********", txnProcessResult.debitAccNo)
        assertEquals("Debit Account Name", txnProcessResult.debitAccName)
        assertEquals(10L, txnProcessResult.total)
        assertEquals(9L, txnProcessResult.totalSuccess)
        assertEquals(1L, txnProcessResult.totalFail)
        assertEquals("One million dong", txnProcessResult.totalAmountText)
    }

    @Test
    fun `test verifyTransaction with null reqValue returns success result`() = runTest {
        val initResponse = InitVerifyTransactionResponse(
            transKey = "verify_trans_key"
        )
        val reqValue: String? = null
        
        val confirmResponse = mockk<TxnProcessResultRes> {
            every { treasuryCode } returns "T001"
            every { admAreaCode } returns null
            every { revAccCode } returns null
            every { revAuthCode } returns null
            every { treasuryName } returns null
            every { admAreaName } returns null
            every { revAccName } returns null
            every { revAuthName } returns null
            every { failTxns } returns null
            every { txnId } returns "TXN001"
            every { totalAmount } returns "1000000"
            every { ccy } returns null
            every { feeTotal } returns null
            every { feeCcy } returns null
            every { feeOpt } returns null
            every { createdDate } returns null
            every { debitAccNo } returns null
            every { debitAccName } returns null
            every { total } returns null
            every { totalSuccess } returns null
            every { totalFail } returns null
            every { totalAmountText } returns null
        }
        val expectedResult = NetworkResult.Success(confirmResponse)

        coEvery {
            governmentServiceRepository.confirmPushTransaction(
                TxnConfirmReq(
                    transKey = "verify_trans_key",
                    confirmValue = ""
                )
            )
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.verifyTransaction(initResponse, reqValue)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        val txnProcessResult = gson.fromJson(successResult.data, TxnProcessResultDMO::class.java)
        assertEquals("T001", txnProcessResult.treasuryCode)
        assertEquals(null, txnProcessResult.failTxns)
    }

    @Test
    fun `test verifyTransaction with null transKey returns success result`() = runTest {
        val initResponse = InitVerifyTransactionResponse(
            transKey = null
        )
        val reqValue = "confirmation_value"
        
        val confirmResponse = mockk<TxnProcessResultRes> {
            every { treasuryCode } returns "T001"
            every { admAreaCode } returns null
            every { revAccCode } returns null
            every { revAuthCode } returns null
            every { treasuryName } returns null
            every { admAreaName } returns null
            every { revAccName } returns null
            every { revAuthName } returns null
            every { failTxns } returns null
            every { txnId } returns "TXN001"
            every { totalAmount } returns null
            every { ccy } returns null
            every { feeTotal } returns null
            every { feeCcy } returns null
            every { feeOpt } returns null
            every { createdDate } returns null
            every { debitAccNo } returns null
            every { debitAccName } returns null
            every { total } returns null
            every { totalSuccess } returns null
            every { totalFail } returns null
            every { totalAmountText } returns null
        }
        val expectedResult = NetworkResult.Success(confirmResponse)

        coEvery {
            governmentServiceRepository.confirmPushTransaction(
                TxnConfirmReq(
                    transKey = "",
                    confirmValue = "confirmation_value"
                )
            )
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.verifyTransaction(initResponse, reqValue)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        val txnProcessResult = gson.fromJson(successResult.data, TxnProcessResultDMO::class.java)
        assertEquals("T001", txnProcessResult.treasuryCode)
    }

    @Test
    fun `test verifyTransaction with empty failTxns returns success result`() = runTest {
        val initResponse = InitVerifyTransactionResponse(
            transKey = "verify_trans_key"
        )
        val reqValue = "confirmation_value"
        
        val confirmResponse = mockk<TxnProcessResultRes> {
            every { treasuryCode } returns "T001"
            every { admAreaCode } returns null
            every { revAccCode } returns null
            every { revAuthCode } returns null
            every { treasuryName } returns null
            every { admAreaName } returns null
            every { revAccName } returns null
            every { revAuthName } returns null
            every { failTxns } returns emptyList()
            every { txnId } returns "TXN001"
            every { totalAmount } returns "1000000"
            every { ccy } returns null
            every { feeTotal } returns null
            every { feeCcy } returns null
            every { feeOpt } returns null
            every { createdDate } returns null
            every { debitAccNo } returns null
            every { debitAccName } returns null
            every { total } returns null
            every { totalSuccess } returns null
            every { totalFail } returns null
            every { totalAmountText } returns null
        }
        val expectedResult = NetworkResult.Success(confirmResponse)

        coEvery {
            governmentServiceRepository.confirmPushTransaction(any())
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.verifyTransaction(initResponse, reqValue)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        val txnProcessResult = gson.fromJson(successResult.data, TxnProcessResultDMO::class.java)
        assertEquals("T001", txnProcessResult.treasuryCode)
        assertEquals(0, txnProcessResult.failTxns?.size)
    }

    @Test
    fun `test verifyTransaction with multiple failTxns returns success result`() = runTest {
        val initResponse = InitVerifyTransactionResponse(
            transKey = "verify_trans_key"
        )
        val reqValue = "confirmation_value"
        
        val failTxnDetail1 = mockk<TransactionResDetail> {
            every { txnId } returns "FAIL001"
            every { message } returns "First failure"
            every { code } returns "ERR001"
        }
        
        val failTxnDetail2 = mockk<TransactionResDetail> {
            every { txnId } returns "FAIL002"
            every { message } returns "Second failure"
            every { code } returns "ERR002"
        }
        
        val confirmResponse = mockk<TxnProcessResultRes> {
            every { treasuryCode } returns "T001"
            every { admAreaCode } returns null
            every { revAccCode } returns null
            every { revAuthCode } returns null
            every { treasuryName } returns null
            every { admAreaName } returns null
            every { revAccName } returns null
            every { revAuthName } returns null
            every { failTxns } returns listOf(failTxnDetail1, failTxnDetail2)
            every { txnId } returns "TXN001"
            every { totalAmount } returns null
            every { ccy } returns null
            every { feeTotal } returns null
            every { feeCcy } returns null
            every { feeOpt } returns null
            every { createdDate } returns null
            every { debitAccNo } returns null
            every { debitAccName } returns null
            every { total } returns 5L
            every { totalSuccess } returns 3L
            every { totalFail } returns 2L
            every { totalAmountText } returns null
        }
        val expectedResult = NetworkResult.Success(confirmResponse)

        coEvery {
            governmentServiceRepository.confirmPushTransaction(any())
        } returns expectedResult

        val result = pushCustomsDutiesTransactionUseCase.verifyTransaction(initResponse, reqValue)

        assertTrue(result is DomainResult.Success)
        val successResult = result as DomainResult.Success
        val txnProcessResult = gson.fromJson(successResult.data, TxnProcessResultDMO::class.java)
        
        assertEquals(2, txnProcessResult.failTxns?.size)
        assertEquals("FAIL001", txnProcessResult.failTxns?.get(0)?.txnId)
        assertEquals("First failure", txnProcessResult.failTxns?.get(0)?.message)
        assertEquals("ERR001", txnProcessResult.failTxns?.get(0)?.code)
        assertEquals("FAIL002", txnProcessResult.failTxns?.get(1)?.txnId)
        assertEquals("Second failure", txnProcessResult.failTxns?.get(1)?.message)
        assertEquals("ERR002", txnProcessResult.failTxns?.get(1)?.code)
        assertEquals(5L, txnProcessResult.total)
        assertEquals(3L, txnProcessResult.totalSuccess)
        assertEquals(2L, txnProcessResult.totalFail)
    }
} 