package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create

import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNotEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.government.service.domain.model.AdministrativeAreaDMO
import vn.com.bidv.feature.government.service.domain.model.BalanceAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAccountDMO
import vn.com.bidv.feature.government.service.domain.model.RevenueAuthorityDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.feature.government.service.domain.model.TransactionTemplateDMO
import vn.com.bidv.feature.government.service.domain.model.TreasuryDMO
import vn.com.bidv.feature.government.service.domain.model.ValidateTxnDMO
import vn.com.bidv.feature.government.service.model.CreateTransactionType
import vn.com.bidv.feature.government.service.model.CustomsFeeInfoStep2Content
import vn.com.bidv.feature.government.service.model.CustomsFeeInfoStep3Content
import vn.com.bidv.feature.government.service.model.GetTransactionDetailPurpose
import vn.com.bidv.feature.government.service.model.TaxPayerEntity
import vn.com.bidv.feature.government.service.model.TaxPayerInfo
import vn.com.bidv.feature.government.service.domain.model.TransactionDetailDMO

class CustomsFeeInfoMainReducerTest {

    private lateinit var reducer: CustomsFeeInfoMainReducer
    private lateinit var initialState: CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState

    @Before
    fun setUp() {
        reducer = CustomsFeeInfoMainReducer()
        initialState = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState()
    }

    // MARK: - Initial State Tests
    @Test
    fun `test initial state has correct default values`() {
        with(initialState) {
            assertEquals(0, currentStep)
            assertNull(step3Content)
        }
    }

    // MARK: - CreateTransactionType Tests
    @Test
    fun `test CreateTransactionType sealed interface hierarchy`() {
        val createNew = CreateTransactionType.CreateNewTransaction
        val createFromTemplate = CreateTransactionType.CreateFromTemplate(
            createMockTransactionTemplate()
        )
        val copyTransaction = CreateTransactionType.CopyTransaction(
            createMockTransactionDetail()
        )

        assertTrue(createNew is CreateTransactionType)
        assertTrue(createFromTemplate is CreateTransactionType)
        assertTrue(copyTransaction is CreateTransactionType)
    }

    // MARK: - Event Handling Tests
    @Test
    fun `test ChangeToStepOne event updates currentStep correctly`() {
        val initialState = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(currentStep = 2)
        val event = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepOne

        val (newState, sideEffect) = reducer.reduce(initialState, event)

        assertEquals(1, newState.currentStep)
        assertNull(sideEffect)
    }

    @Test
    fun `test ChangeToStepTwo event updates state and content correctly`() {
        val listPayment = listOf(createMockTaxPayment())
        val taxPayerInfo = TaxPayerInfo("123456", "John Doe", "123 Main St")
        val taxDelegatorInfo = TaxPayerInfo("789012", "Jane Smith", "456 Oak Ave")
        val event = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepTwo(
            listPayment = listPayment,
            taxPayerInfo = taxPayerInfo,
            taxDelegatorInfo = taxDelegatorInfo,
            modeDelegate = true
        )

        val (newState, sideEffect) = reducer.reduce(initialState, event)

        assertEquals(2, newState.currentStep)
        assertNull(sideEffect)

        val step2Content = newState.getStep2Content()
        assertEquals(listPayment, step2Content.listPayment)
        assertEquals(taxPayerInfo, step2Content.taxPayerInfo)
        assertEquals(taxDelegatorInfo, step2Content.taxDelegatorInfo)
    }

    @Test
    fun `test ChangeToStepThree event updates state correctly`() {
        val step3Content = createMockStep3Content()
        val event =
            CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepThree(step3Content)

        val (newState, sideEffect) = reducer.reduce(initialState, event)

        assertEquals(3, newState.currentStep)
        assertEquals(step3Content, newState.step3Content)
        assertNull(sideEffect)
    }

    @Test
    fun `test PopToStepTwo event updates currentStep correctly`() {
        val initialState = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(currentStep = 3)
        val event = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.PopToStepTwo

        val (newState, sideEffect) = reducer.reduce(initialState, event)

        assertEquals(2, newState.currentStep)
        assertNull(sideEffect)
    }

    @Test
    fun `test SetDataTransTemp event updates createTransactionType correctly`() {
        val transTemplate = createMockTransactionTemplate()
        val event =
            CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.SetDataTransTemp(transTemplate)

        val (newState, sideEffect) = reducer.reduce(initialState, event)

        assertEquals(initialState.currentStep, newState.currentStep)
        // Verify that the transaction type was updated (indirectly through step1 content)
        val step1Content = newState.getStep1Content()
        assertNotNull(step1Content)
    }

    @Test
    fun `test SetDataTransDetail event updates createTransactionType correctly`() {
        val transDetail = createMockTransactionDetail()
        val event = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.SetDataTransDetail(
            GetTransactionDetailPurpose.COPY_TRANSACTION,
            transDetail
        )

        val (newState, sideEffect) = reducer.reduce(initialState, event)

        assertEquals(initialState.currentStep, newState.currentStep)
        assertNotNull(sideEffect)
        // Verify that the transaction type was updated (indirectly through step1 content)
        val step1Content = newState.getStep1Content()
        assertNotNull(step1Content)
    }

    // MARK: - getStep1Content() Method Tests
    @Test
    fun `test getStep1Content returns null for CreateNewTransaction`() {
        val state = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            createTransactionType = CreateTransactionType.CreateNewTransaction
        )

        val step1Content = state.getStep1Content()

        assertNull(step1Content)
    }

    @Test
    fun `test getStep1Content returns correct content for CreateFromTemplate`() {
        val template = createMockTransactionTemplate(
            taxCode = "*********",
            payerName = "Template Company",
            payerAddr = "Template Address",
            altTaxCode = "*********" // This makes it delegate mode
        )
        val state = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            createTransactionType = CreateTransactionType.CreateFromTemplate(template)
        )

        val step1Content = state.getStep1Content()

        assertNotNull(step1Content)
        assertTrue(step1Content!!.modeDelegate)
        assertEquals("*********", step1Content.taxPayerInfo?.taxId)
        assertEquals("Template Company", step1Content.taxPayerInfo?.name)
        assertEquals("Template Address", step1Content.taxPayerInfo?.address)
        assertTrue(step1Content.shouldFetchTaxPayerInfo)
    }

    @Test
    fun `test getStep1Content returns correct content for CopyTransaction`() {
        val transDetail = createMockTransactionDetail(
            payerTaxCode = "*********",
            payerName = "Copy Company",
            payerAddress = "Copy Address",
            altTaxCode = "*********" // This makes it delegate mode
        )
        val state = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            createTransactionType = CreateTransactionType.CopyTransaction(transDetail)
        )

        val step1Content = state.getStep1Content()

        assertNotNull(step1Content)
        assertTrue(step1Content!!.modeDelegate)
        assertEquals("*********", step1Content.taxPayerInfo?.taxId)
        assertEquals("Copy Company", step1Content.taxPayerInfo?.name)
        assertEquals("Copy Address", step1Content.taxPayerInfo?.address)
        assertTrue(step1Content.shouldFetchTaxPayerInfo)
    }

    @Test
    fun `test getStep1Content returns correct content for EditTransaction`() {
        val transDetail = createMockTransactionDetail(
            payerTaxCode = "*********",
            payerName = "Edit Company",
            payerAddress = "Edit Address",
            altTaxCode = "*********",
            altPayerName = "Delegate Company",
            altPayerAddress = "Delegate Address"
        )
        val state = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            createTransactionType = CreateTransactionType.EditTransaction(transDetail)
        )

        val step1Content = state.getStep1Content()

        assertNotNull(step1Content)
        assertTrue(step1Content!!.modeDelegate)
        assertEquals("*********", step1Content.taxPayerInfo?.taxId)
        assertEquals("Edit Company", step1Content.taxPayerInfo?.name)
        assertEquals("Edit Address", step1Content.taxPayerInfo?.address)
        assertEquals("*********", step1Content.taxDelegatorInfo?.taxId)
        assertEquals("Delegate Company", step1Content.taxDelegatorInfo?.name)
        assertEquals("Delegate Address", step1Content.taxDelegatorInfo?.address)
        assertFalse(step1Content.shouldFetchTaxPayerInfo)
        assertFalse(step1Content.shouldShowDelegatePicker)
    }

    // MARK: - getStep2Content() Method Tests
    @Test
    fun `test getStep2Content returns default content when step2Content is null`() {
        val state = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            step2Content = null,
            createTransactionType = CreateTransactionType.CreateNewTransaction
        )

        val step2Content = state.getStep2Content()

        assertNotNull(step2Content)
        assertTrue(step2Content.listPayment.isEmpty())
        assertEquals(TaxPayerInfo("", "", ""), step2Content.taxPayerInfo)
        assertEquals(TaxPayerInfo("", "", ""), step2Content.taxDelegatorInfo)
        assertEquals(TaxPayerEntity.INDIVIDUAL, step2Content.taxPayerEntitySelected)
    }

    @Test
    fun `test getStep2Content returns step2Content for CreateNewTransaction`() {
        val originalStep2Content = CustomsFeeInfoStep2Content(
            listPayment = listOf(createMockTaxPayment()),
            taxPayerInfo = TaxPayerInfo("123", "Test", "Address"),
            taxDelegatorInfo = TaxPayerInfo("456", "Delegate", "Delegate Address"),
            noteToApprover = "Test note",
            priorityTransaction = true,
            customerTransactionCode = "CUST123"
        )
        val state = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            step2Content = originalStep2Content,
            createTransactionType = CreateTransactionType.CreateNewTransaction
        )

        val step2Content = state.getStep2Content()

        assertEquals(originalStep2Content, step2Content)
    }

    @Test
    fun `test getStep2Content returns merged content for CreateFromTemplate`() {
        val originalStep2Content = CustomsFeeInfoStep2Content(
            listPayment = listOf(createMockTaxPayment()),
            taxPayerInfo = TaxPayerInfo("123", "Test", "Address"),
            taxDelegatorInfo = TaxPayerInfo("456", "Delegate", "Delegate Address"),
            noteToApprover = "Original note",
            priorityTransaction = true,
            customerTransactionCode = "CUST123"
        )
        val template = createMockTransactionTemplate(
            treasuryCode = "T001",
            treasuryName = "Treasury 1",
            revAuthCode = "RA001",
            revAuthName = "Revenue Authority 1",
            revAccCode = "RAC001",
            revAccName = "Revenue Account 1",
            admAreaCode = "AA001",
            admAreaName = "Admin Area 1",
            payerType = 1 // BUSINESS
        )
        val state = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            step2Content = originalStep2Content,
            createTransactionType = CreateTransactionType.CreateFromTemplate(template)
        )

        val step2Content = state.getStep2Content()

        assertEquals(originalStep2Content.listPayment, step2Content.listPayment)
        assertEquals(originalStep2Content.taxPayerInfo, step2Content.taxPayerInfo)
        assertEquals(originalStep2Content.taxDelegatorInfo, step2Content.taxDelegatorInfo)
        assertEquals("T001", step2Content.treasurySelected?.treasuryCode)
        assertEquals("Treasury 1", step2Content.treasurySelected?.treasuryName)
        assertEquals("RA001", step2Content.revenueAuthoritySelected?.revAuthCode)
        assertEquals("Revenue Authority 1", step2Content.revenueAuthoritySelected?.revAuthName)
        assertEquals("RAC001", step2Content.revenueAccountSelected?.revAccCode)
        assertEquals("Revenue Account 1", step2Content.revenueAccountSelected?.revAccName)
        assertEquals("AA001", step2Content.administrativeAreaSelected?.admAreaCode)
        assertEquals("Admin Area 1", step2Content.administrativeAreaSelected?.admAreaName)
        assertEquals(TaxPayerEntity.BUSINESS, step2Content.taxPayerEntitySelected)
        assertNull(step2Content.noteToApprover)
    }

    @Test
    fun `test getStep2Content returns merged content for CopyTransaction`() {
        val originalStep2Content = CustomsFeeInfoStep2Content(
            listPayment = listOf(createMockTaxPayment()),
            taxPayerInfo = TaxPayerInfo("123", "Test", "Address"),
            taxDelegatorInfo = TaxPayerInfo("456", "Delegate", "Delegate Address"),
            noteToApprover = null,
            priorityTransaction = null,
            customerTransactionCode = null
        )
        val transDetail = createMockTransactionDetail(
            treasuryCode = "T002",
            treasuryName = "Treasury 2",
            authorityCode = "RA002",
            authorityName = "Revenue Authority 2",
            revAccCode = "RAC002",
            revAccName = "Revenue Account 2",
            admAreaCode = "AA002",
            admAreaName = "Admin Area 2",
            payerType = 2 // INDIVIDUAL
        )
        val state = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            step2Content = originalStep2Content,
            createTransactionType = CreateTransactionType.CopyTransaction(transDetail)
        )

        val step2Content = state.getStep2Content()

        assertEquals(originalStep2Content.listPayment, step2Content.listPayment)
        assertEquals(originalStep2Content.taxPayerInfo, step2Content.taxPayerInfo)
        assertEquals(originalStep2Content.taxDelegatorInfo, step2Content.taxDelegatorInfo)
        assertEquals("T002", step2Content.treasurySelected?.treasuryCode)
        assertEquals("Treasury 2", step2Content.treasurySelected?.treasuryName)
        assertEquals("RA002", step2Content.revenueAuthoritySelected?.revAuthCode)
        assertEquals("Revenue Authority 2", step2Content.revenueAuthoritySelected?.revAuthName)
        assertEquals("RAC002", step2Content.revenueAccountSelected?.revAccCode)
        assertEquals("Revenue Account 2", step2Content.revenueAccountSelected?.revAccName)
        assertEquals("AA002", step2Content.administrativeAreaSelected?.admAreaCode)
        assertEquals("Admin Area 2", step2Content.administrativeAreaSelected?.admAreaName)
        assertEquals(TaxPayerEntity.INDIVIDUAL, step2Content.taxPayerEntitySelected)
    }

    // MARK: - Data Class Tests
    @Test
    fun `test CustomsFeeInfoMainViewState data class properties and immutability`() {
        val step3Content = createMockStep3Content()
        val state1 = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            currentStep = 2,
            step3Content = step3Content
        )
        val state2 = state1.copy(currentStep = 3)

        assertEquals(2, state1.currentStep)
        assertEquals(step3Content, state1.step3Content)
        assertEquals(3, state2.currentStep)
        assertEquals(step3Content, state2.step3Content)
        assertNotEquals(state1, state2)
    }

    @Test
    fun `test CustomsFeeInfoMainViewState equals and hashCode`() {
        val step3Content = createMockStep3Content()
        val state1 = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            currentStep = 2,
            step3Content = step3Content
        )
        val state2 = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            currentStep = 2,
            step3Content = step3Content
        )
        val state3 = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState(
            currentStep = 3,
            step3Content = step3Content
        )

        assertEquals(state1, state2)
        assertEquals(state1.hashCode(), state2.hashCode())
        assertNotEquals(state1, state3)
        assertNotEquals(state1.hashCode(), state3.hashCode())
    }

    // MARK: - Event Type Tests
    @Test
    fun `test all events implement ViewEvent interface`() {
        val events = listOf(
            CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepOne,
            CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepTwo(
                listOf(), TaxPayerInfo("", "", ""), TaxPayerInfo("", "", ""), false
            ),
            CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepThree(
                createMockStep3Content()
            ),
            CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.PopToStepTwo,
            CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.SetDataTransTemp(
                createMockTransactionTemplate()
            ),
            CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.SetDataTransDetail(
                GetTransactionDetailPurpose.EDIT_TRANSACTION,
                createMockTransactionDetail()
            )
        )

        events.forEach { event ->
            assertTrue(event is CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent)
            assertTrue(event is vn.com.bidv.common.patterns.mvi.ViewEvent)
        }
    }

    @Test
    fun `test reducer reduce method returns correct types`() {
        val event = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepOne
        val result = reducer.reduce(initialState, event)

        assertTrue(result is Pair<*, *>)
        assertTrue(result.first is CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState)
        assertNull(result.second)
    }

    @Test
    fun `test reducer state immutability`() {
        val event = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepOne
        val (newState, _) = reducer.reduce(initialState, event)

        assertEquals(0, initialState.currentStep)
        assertEquals(1, newState.currentStep)
        assertNotEquals(initialState, newState)
    }

    @Test
    fun `test TaxPayerEntity fromInt method handles edge cases`() {
        assertEquals(TaxPayerEntity.BUSINESS, TaxPayerEntity.fromInt(1))
        assertEquals(TaxPayerEntity.INDIVIDUAL, TaxPayerEntity.fromInt(2))
        assertNull(TaxPayerEntity.fromInt(0))
        assertNull(TaxPayerEntity.fromInt(-1))
        assertNull(TaxPayerEntity.fromInt(null))
    }

    // MARK: - Helper Methods
    private fun createMockTaxPayment(): TaxPaymentDMO {
        return TaxPaymentDMO(
            eiTypeCode = "EI001",
            eiTypeName = "Export Type",
            taxTypeCode = "T001",
            taxTypeName = "Tax Type 1",
            ccCode = "CC001",
            ccName = "Currency 1",
            chapterCode = "CH001",
            chapterName = "Chapter 1",
            ecCode = "EC001",
            ecName = "Economic 1",
            amount = "1000000",
            ccy = "VND",
            declarationDate = "2024-01-01",
            declarationNo = "DEC001",
            transDesc = "Test payment",
            payerType = 1,
            payerTypeName = "Business",
            treasuryCode = "T001",
            treasuryName = "Treasury 1",
            admAreaCode = "AA001",
            admAreaName = "Admin Area 1",
            revAccCode = "RAC001",
            revAccName = "Revenue Account 1",
            revAuthCode = "RA001",
            revAuthName = "Revenue Authority 1"
        )
    }

    private fun createMockTransactionTemplate(
        taxCode: String? = "*********",
        altTaxCode: String? = null,
        payerName: String? = "Test Company",
        payerAddr: String? = "Test Address",
        treasuryCode: String? = null,
        treasuryName: String? = null,
        revAuthCode: String? = null,
        revAuthName: String? = null,
        revAccCode: String? = null,
        revAccName: String? = null,
        admAreaCode: String? = null,
        admAreaName: String? = null,
        payerType: Int? = null
    ): TransactionTemplateDMO {
        return TransactionTemplateDMO(
            taxCode = taxCode,
            altTaxCode = altTaxCode,
            payerName = payerName,
            payerAddr = payerAddr,
            treasuryCode = treasuryCode,
            treasuryName = treasuryName,
            revAuthCode = revAuthCode,
            revAuthName = revAuthName,
            revAccCode = revAccCode,
            revAccName = revAccName,
            admAreaCode = admAreaCode,
            admAreaName = admAreaName,
            payerType = payerType,
            taxItems = listOf(createMockTaxPayment())
        )
    }

    private fun createMockTransactionDetail(
        payerTaxCode: String? = "*********",
        altTaxCode: String? = null,
        altPayerName: String? = null,
        altPayerAddress: String? = null,
        payerName: String? = "Test Company",
        payerAddress: String? = "Test Address",
        treasuryCode: String? = null,
        treasuryName: String? = null,
        authorityCode: String? = null,
        authorityName: String? = null,
        revAccCode: String? = null,
        revAccName: String? = null,
        admAreaCode: String? = null,
        admAreaName: String? = null,
        payerType: Int? = null
    ): TransactionDetailDMO {
        return TransactionDetailDMO(
            transactionId = "TXN001",
            payerTaxCode = payerTaxCode,
            altPayerName = altPayerName,
            altPayerAddress = altPayerAddress,
            altTaxCode = altTaxCode,
            payerName = payerName,
            payerAddress = payerAddress,
            treasuryCode = treasuryCode,
            treasuryName = treasuryName,
            authorityCode = authorityCode,
            authorityName = authorityName,
            revAccCode = revAccCode,
            revAccName = revAccName,
            admAreaCode = admAreaCode,
            admAreaName = admAreaName,
            payerType = payerType,
            taxItems = listOf(createMockTaxPayment())
        )
    }

    private fun createMockStep3Content(): CustomsFeeInfoStep3Content {
        return CustomsFeeInfoStep3Content(
            taxPayerInfo = TaxPayerInfo("*********", "Test Company", "Test Address"),
            taxDelegatorInfo = TaxPayerInfo("*********", "Delegate Company", "Delegate Address"),
            listPayment = listOf(createMockTaxPayment()),
            debitAccount = BalanceAccountDMO("ACC001", "Test Account", "VND", "1000000"),
            treasury = TreasuryDMO("T001", "Treasury 1"),
            revenueAuthority = RevenueAuthorityDMO("RA001", "Revenue Authority 1"),
            revenueAccount = RevenueAccountDMO("RAC001", "Revenue Account 1"),
            administrativeArea = AdministrativeAreaDMO("AA001", "Admin Area 1"),
            taxPayerEntity = TaxPayerEntity.BUSINESS,
            noteToApprover = "Test note",
            validateResult = ValidateTxnDMO(),
            isEditingTransaction = false,
            priorityTransaction = true,
            customerTransactionCode = "123ABC456DEF",
            benBankName = "Test Bank",
            benBankCode = "BB001",
        )
    }

    // MARK: - Edge Case Tests
    @Test
    fun `test multiple sequential state updates preserve data correctly`() {
        var currentState = initialState

        // Set template
        val template = createMockTransactionTemplate()
        val setTemplateEvent =
            CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.SetDataTransTemp(template)
        val (state1, _) = reducer.reduce(currentState, setTemplateEvent)
        currentState = state1

        // Change to step 2
        val changeStepEvent = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepTwo(
            listOf(createMockTaxPayment()),
            TaxPayerInfo("123", "Name", "Address"),
            TaxPayerInfo("456", "Delegate", "Delegate Address"),
            false
        )
        val (state2, _) = reducer.reduce(currentState, changeStepEvent)
        currentState = state2

        // Change to step 3
        val step3Event = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.ChangeToStepThree(
            createMockStep3Content()
        )
        val (finalState, _) = reducer.reduce(currentState, step3Event)

        // Verify all data is preserved
        assertEquals(3, finalState.currentStep)
        assertNotNull(finalState.step3Content)
        assertNotNull(finalState.getStep1Content())
        assertNotNull(finalState.getStep2Content())
    }

    @Test
    fun `test step navigation preserves existing content`() {
        val step3Content = createMockStep3Content()
        val stateWithStep3 = initialState.copy(
            currentStep = 3,
            step3Content = step3Content
        )

        val popEvent = CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent.PopToStepTwo
        val (newState, _) = reducer.reduce(stateWithStep3, popEvent)

        assertEquals(2, newState.currentStep)
        assertEquals(step3Content, newState.step3Content) // Content preserved
    }
} 