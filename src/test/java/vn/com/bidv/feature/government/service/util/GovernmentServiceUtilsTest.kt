package vn.com.bidv.feature.government.service.util

import org.junit.Assert.assertEquals
import org.junit.Test
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.domain.model.TaxPaymentDMO
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class GovernmentServiceUtilsTest {

    // MARK: - formatCodeName Tests

    @Test
    fun `formatCodeName with both code and name non-empty should return code - name format`() {
        val result = GovernmentServiceUtils.formatCodeName("001", "Test Name")
        assertEquals("001 - Test Name", result)
    }

    @Test
    fun `formatCodeName with valid code and empty name should return code only`() {
        val result = GovernmentServiceUtils.formatCodeName("002", "")
        assertEquals("002", result)
    }

    @Test
    fun `formatCodeName with valid code and null name should return code only`() {
        val result = GovernmentServiceUtils.formatCodeName("003", null)
        assertEquals("003", result)
    }

    @Test
    fun `formatCodeName with empty code and valid name should return name only`() {
        val result = GovernmentServiceUtils.formatCodeName("", "Valid Name")
        assertEquals("Valid Name", result)
    }

    @Test
    fun `formatCodeName with null code and valid name should return name only`() {
        val result = GovernmentServiceUtils.formatCodeName(null, "Valid Name")
        assertEquals("Valid Name", result)
    }

    @Test
    fun `formatCodeName with both code and name empty should return default empty string`() {
        val result = GovernmentServiceUtils.formatCodeName("", "")
        assertEquals("", result)
    }

    @Test
    fun `formatCodeName with both code and name null should return default empty string`() {
        val result = GovernmentServiceUtils.formatCodeName(null, null)
        assertEquals("", result)
    }

    @Test
    fun `formatCodeName with null code and empty name should return default empty string`() {
        val result = GovernmentServiceUtils.formatCodeName(null, "")
        assertEquals("", result)
    }

    @Test
    fun `formatCodeName with empty code and null name should return default empty string`() {
        val result = GovernmentServiceUtils.formatCodeName("", null)
        assertEquals("", result)
    }

    @Test
    fun `formatCodeName with custom empty string parameter`() {
        val result = GovernmentServiceUtils.formatCodeName("", "", "N/A")
        assertEquals("N/A", result)
    }

    @Test
    fun `formatCodeName with custom empty string but valid values should ignore custom empty string`() {
        val result = GovernmentServiceUtils.formatCodeName("001", "Name", "N/A")
        assertEquals("001 - Name", result)
    }

    @Test
    fun `formatCodeName with whitespace-only code should be treated as non-empty`() {
        val result = GovernmentServiceUtils.formatCodeName("   ", "Valid Name")
        assertEquals("    - Valid Name", result)
    }

    @Test
    fun `formatCodeName with whitespace-only name should be treated as non-empty`() {
        val result = GovernmentServiceUtils.formatCodeName("001", "   ")
        assertEquals("001 -    ", result)
    }

    @Test
    fun `formatCodeName with special characters in code and name`() {
        val result = GovernmentServiceUtils.formatCodeName("ABC@123", "Name#$%")
        assertEquals("ABC@123 - Name#$%", result)
    }

    @Test
    fun `formatCodeName with unicode characters`() {
        val result = GovernmentServiceUtils.formatCodeName("VN001", "Tên Tiếng Việt")
        assertEquals("VN001 - Tên Tiếng Việt", result)
    }

    @Test
    fun `formatCodeName with newline characters`() {
        val result = GovernmentServiceUtils.formatCodeName("001", "Line1\nLine2")
        assertEquals("001 - Line1\nLine2", result)
    }

    @Test
    fun `formatCodeName with treasury code example`() {
        val result = GovernmentServiceUtils.formatCodeName("T001", "Kho bạc Nhà nước Trung ương")
        assertEquals("T001 - Kho bạc Nhà nước Trung ương", result)
    }

    @Test
    fun `formatCodeName with administrative area code example`() {
        val result = GovernmentServiceUtils.formatCodeName("01", "Thành phố Hà Nội")
        assertEquals("01 - Thành phố Hà Nội", result)
    }

    @Test
    fun `formatCodeName with very long strings`() {
        val longCode = "A".repeat(100)
        val longName = "B".repeat(200)
        val result = GovernmentServiceUtils.formatCodeName(longCode, longName)
        assertEquals("$longCode - $longName", result)
    }

    @Test
    fun `formatCodeName with single character inputs`() {
        val result = GovernmentServiceUtils.formatCodeName("A", "B")
        assertEquals("A - B", result)
    }

    @Test
    fun `formatCodeName comprehensive matrix test`() {
        val testCases = listOf(
            Triple(null, null, ""),
            Triple(null, "", ""),
            Triple("", null, ""),
            Triple("", "", ""),
            Triple("code", null, "code"),
            Triple("code", "", "code"),
            Triple(null, "name", "name"),
            Triple("", "name", "name"),
            Triple("code", "name", "code - name")
        )

        testCases.forEach { (code, name, expected) ->
            val result = GovernmentServiceUtils.formatCodeName(code, name)
            assertEquals("Failed for code='$code', name='$name'", expected, result)
        }
    }

    // MARK: - isPaymentDuplicated Tests

    @Test
    fun `isPaymentDuplicated with identical payments should return true`() {
        val payment1 = createTaxPayment(
            declarationNo = "DECL001",
            declarationDate = "2024-01-01",
            ecCode = "EC001",
            chapterCode = "CH001",
            amount = "1000000",
            ccy = "VND",
            taxTypeCode = "TAX001",
            eiTypeCode = "EI001",
            ccCode = "CC001",
            transDesc = "Test transaction",
            treasuryCode = "TREAS001",
            revAccCode = "REV001",
            revAuthCode = "AUTH001",
            admAreaCode = "AREA001"
        )

        val payment2 = payment1.copy()

        assertTrue(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different declarationNo should return false`() {
        val payment1 = createTaxPayment(declarationNo = "DECL001")
        val payment2 = createTaxPayment(declarationNo = "DECL002")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different declarationDate should return false`() {
        val payment1 = createTaxPayment(declarationDate = "2024-01-01")
        val payment2 = createTaxPayment(declarationDate = "2024-01-02")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different ecCode should return false`() {
        val payment1 = createTaxPayment(ecCode = "EC001")
        val payment2 = createTaxPayment(ecCode = "EC002")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different chapterCode should return false`() {
        val payment1 = createTaxPayment(chapterCode = "CH001")
        val payment2 = createTaxPayment(chapterCode = "CH002")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different amount should return false`() {
        val payment1 = createTaxPayment(amount = "1000000")
        val payment2 = createTaxPayment(amount = "2000000")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different ccy should return false`() {
        val payment1 = createTaxPayment(ccy = "VND")
        val payment2 = createTaxPayment(ccy = "USD")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different taxTypeCode should return false`() {
        val payment1 = createTaxPayment(taxTypeCode = "TAX001")
        val payment2 = createTaxPayment(taxTypeCode = "TAX002")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different eiTypeCode should return false`() {
        val payment1 = createTaxPayment(eiTypeCode = "EI001")
        val payment2 = createTaxPayment(eiTypeCode = "EI002")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different ccCode should return false`() {
        val payment1 = createTaxPayment(ccCode = "CC001")
        val payment2 = createTaxPayment(ccCode = "CC002")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different transDesc should return false`() {
        val payment1 = createTaxPayment(transDesc = "Description 1")
        val payment2 = createTaxPayment(transDesc = "Description 2")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with null treasuryCode in both payments should return true`() {
        val payment1 = createTaxPayment(treasuryCode = null)
        val payment2 = createTaxPayment(treasuryCode = null)

        assertTrue(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with one null treasuryCode should return true`() {
        val payment1 = createTaxPayment(treasuryCode = "TREAS001")
        val payment2 = createTaxPayment(treasuryCode = null)

        assertTrue(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different non-null treasuryCode should return false`() {
        val payment1 = createTaxPayment(treasuryCode = "TREAS001")
        val payment2 = createTaxPayment(treasuryCode = "TREAS002")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with null revAccCode in both payments should return true`() {
        val payment1 = createTaxPayment(revAccCode = null)
        val payment2 = createTaxPayment(revAccCode = null)

        assertTrue(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with one null revAccCode should return true`() {
        val payment1 = createTaxPayment(revAccCode = "REV001")
        val payment2 = createTaxPayment(revAccCode = null)

        assertTrue(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different non-null revAccCode should return false`() {
        val payment1 = createTaxPayment(revAccCode = "REV001")
        val payment2 = createTaxPayment(revAccCode = "REV002")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with null revAuthCode in both payments should return true`() {
        val payment1 = createTaxPayment(revAuthCode = null)
        val payment2 = createTaxPayment(revAuthCode = null)

        assertTrue(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with one null revAuthCode should return true`() {
        val payment1 = createTaxPayment(revAuthCode = "AUTH001")
        val payment2 = createTaxPayment(revAuthCode = null)

        assertTrue(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different non-null revAuthCode should return false`() {
        val payment1 = createTaxPayment(revAuthCode = "AUTH001")
        val payment2 = createTaxPayment(revAuthCode = "AUTH002")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with null admAreaCode in both payments should return true`() {
        val payment1 = createTaxPayment(admAreaCode = null)
        val payment2 = createTaxPayment(admAreaCode = null)

        assertTrue(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with one null admAreaCode should return true`() {
        val payment1 = createTaxPayment(admAreaCode = "AREA001")
        val payment2 = createTaxPayment(admAreaCode = null)

        assertTrue(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    @Test
    fun `isPaymentDuplicated with different non-null admAreaCode should return false`() {
        val payment1 = createTaxPayment(admAreaCode = "AREA001")
        val payment2 = createTaxPayment(admAreaCode = "AREA002")

        assertFalse(GovernmentServiceUtils.isPaymentDuplicated(payment1, payment2))
    }

    // MARK: - getCurrencyMaxLength Tests

    @Test
    fun `getCurrencyMaxLength with VND currency should return 15`() {
        val currencies = listOf(SdkBaseConstants.MoneyCurrencyConstants.VND)
        val result = GovernmentServiceUtils.getCurrencyMaxLength(currencies)
        assertEquals(Constants.MAX_LENGTH_TEXT_INPUT_15, result)
    }

    @Test
    fun `getCurrencyMaxLength with CLP currency should return 15`() {
        val currencies = listOf(SdkBaseConstants.MoneyCurrencyConstants.CLP)
        val result = GovernmentServiceUtils.getCurrencyMaxLength(currencies)
        assertEquals(Constants.MAX_LENGTH_TEXT_INPUT_15, result)
    }

    @Test
    fun `getCurrencyMaxLength with JPY currency should return 15`() {
        val currencies = listOf(SdkBaseConstants.MoneyCurrencyConstants.JPY)
        val result = GovernmentServiceUtils.getCurrencyMaxLength(currencies)
        assertEquals(Constants.MAX_LENGTH_TEXT_INPUT_15, result)
    }

    @Test
    fun `getCurrencyMaxLength with KRW currency should return 15`() {
        val currencies = listOf(SdkBaseConstants.MoneyCurrencyConstants.KRW)
        val result = GovernmentServiceUtils.getCurrencyMaxLength(currencies)
        assertEquals(Constants.MAX_LENGTH_TEXT_INPUT_15, result)
    }

    @Test
    fun `getCurrencyMaxLength with USD currency should return 13`() {
        val currencies = listOf("USD")
        val result = GovernmentServiceUtils.getCurrencyMaxLength(currencies)
        assertEquals(Constants.MAX_LENGTH_TEXT_INPUT_13, result)
    }

    @Test
    fun `getCurrencyMaxLength with EUR currency should return 13`() {
        val currencies = listOf("EUR")
        val result = GovernmentServiceUtils.getCurrencyMaxLength(currencies)
        assertEquals(Constants.MAX_LENGTH_TEXT_INPUT_13, result)
    }

    @Test
    fun `getCurrencyMaxLength with null list should return 13`() {
        val result = GovernmentServiceUtils.getCurrencyMaxLength(null)
        assertEquals(Constants.MAX_LENGTH_TEXT_INPUT_13, result)
    }

    @Test
    fun `getCurrencyMaxLength with empty list should return 13`() {
        val currencies = emptyList<String>()
        val result = GovernmentServiceUtils.getCurrencyMaxLength(currencies)
        assertEquals(Constants.MAX_LENGTH_TEXT_INPUT_13, result)
    }

    @Test
    fun `getCurrencyMaxLength with multiple currencies should use first currency`() {
        val currencies = listOf(SdkBaseConstants.MoneyCurrencyConstants.VND, "USD", "EUR")
        val result = GovernmentServiceUtils.getCurrencyMaxLength(currencies)
        assertEquals(Constants.MAX_LENGTH_TEXT_INPUT_15, result)
    }

    @Test
    fun `getCurrencyMaxLength with mixed currencies starting with non-special should return 13`() {
        val currencies = listOf("USD", SdkBaseConstants.MoneyCurrencyConstants.VND)
        val result = GovernmentServiceUtils.getCurrencyMaxLength(currencies)
        assertEquals(Constants.MAX_LENGTH_TEXT_INPUT_13, result)
    }

    // MARK: - Helper Methods

    private fun createTaxPayment(
        declarationNo: String = "DEFAULT_DECL",
        declarationDate: String = "2024-01-01",
        ecCode: String = "DEFAULT_EC",
        chapterCode: String = "DEFAULT_CH",
        amount: String = "1000000",
        ccy: String = "VND",
        taxTypeCode: String = "DEFAULT_TAX",
        eiTypeCode: String = "DEFAULT_EI",
        ccCode: String = "DEFAULT_CC",
        transDesc: String = "Default transaction",
        treasuryCode: String? = "DEFAULT_TREAS",
        revAccCode: String? = "DEFAULT_REV",
        revAuthCode: String? = "DEFAULT_AUTH",
        admAreaCode: String? = "DEFAULT_AREA",
        eiTypeName: String? = "Default EI Type",
        taxTypeName: String? = "Default Tax Type",
        ccName: String? = "Default CC Name",
        chapterName: String? = "Default Chapter",
        ecName: String? = "Default EC Name",
        payerType: Int? = 1,
        payerTypeName: String? = "Default Payer Type",
        treasuryName: String? = "Default Treasury",
        admAreaName: String? = "Default Area",
        revAccName: String? = "Default Rev Account",
        revAuthName: String? = "Default Rev Authority"
    ): TaxPaymentDMO {
        return TaxPaymentDMO(
            eiTypeCode = eiTypeCode,
            eiTypeName = eiTypeName,
            taxTypeCode = taxTypeCode,
            taxTypeName = taxTypeName,
            ccCode = ccCode,
            ccName = ccName,
            chapterCode = chapterCode,
            chapterName = chapterName,
            ecCode = ecCode,
            ecName = ecName,
            amount = amount,
            ccy = ccy,
            declarationDate = declarationDate,
            declarationNo = declarationNo,
            transDesc = transDesc,
            payerType = payerType,
            payerTypeName = payerTypeName,
            treasuryCode = treasuryCode,
            treasuryName = treasuryName,
            admAreaCode = admAreaCode,
            admAreaName = admAreaName,
            revAccCode = revAccCode,
            revAccName = revAccName,
            revAuthCode = revAuthCode,
            revAuthName = revAuthName
        )
    }
}