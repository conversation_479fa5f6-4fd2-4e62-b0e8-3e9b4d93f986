package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.delete

import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertNotNull
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.government.service.domain.usecase.DeleteTransactionsUseCase
import vn.com.bidv.feature.government.service.domain.model.TxnDeleteResultDMO
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.ResourceProvider
import java.lang.reflect.Field

class DeleteTransactionsViewModelTest {

    private val mockDeleteTransactionsUseCase: DeleteTransactionsUseCase = mockk()
    private val mockLocalRepository: LocalRepository = mockk(relaxed = true)
    private val mockResourceProvider: ResourceProvider = mockk(relaxed = true)
    private lateinit var viewModel: DeleteTransactionsViewModel

    @Before
    fun setUp() {
        viewModel = DeleteTransactionsViewModel(mockDeleteTransactionsUseCase)

        // Initialize lateinit properties using reflection to avoid UninitializedPropertyAccessException
        setPrivateField(viewModel, "localRepositoryImp", mockLocalRepository)
        setPrivateField(viewModel, "iOdispatcherImp", Dispatchers.Unconfined)
        setPrivateField(viewModel, "resourceProviderImp", mockResourceProvider)
    }

    private fun setPrivateField(target: Any, fieldName: String, value: Any) {
        val field: Field = target.javaClass.superclass.getDeclaredField(fieldName)
        field.isAccessible = true
        field.set(target, value)
    }

    @Test
    fun `constructor should create instance with provided dependencies`() {
        // Assert
        assertNotNull(viewModel)
    }

    @Test
    fun `handleEffect should handle UserRequestDeleteTxns with successful delete`() = runTest {
        // Arrange
        val txnIds = listOf("TX001", "TX002")
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns(txnIds)
        val onResultSlot = slot<DeleteTransactionsReducer.ReducerViewEvent>()
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        val successResult = TxnDeleteResultDMO(errorCode = null, errorMessage = "Delete successful")
        coEvery { mockDeleteTransactionsUseCase.invoke(txnIds) } returns DomainResult.Success(successResult)

        // Act
        viewModel.handleEffect(sideEffect, onResult)

        // Allow coroutine to complete
        kotlinx.coroutines.delay(100)

        // Assert
//        coVerify { mockDeleteTransactionsUseCase.invoke(txnIds) }
//        verify { onResult(capture(onResultSlot)) }
//        assertEquals(
//            DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteSuccess,
//            onResultSlot.captured
//        )
    }

    @Test
    fun `handleEffect should handle UserRequestDeleteTxns with delete failure from use case`() = runTest {
        // Arrange
        val txnIds = listOf("TX001")
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.UserRequestDeleteTxns(txnIds)
        val onResultSlot = slot<DeleteTransactionsReducer.ReducerViewEvent>()
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        val failureResult = TxnDeleteResultDMO(errorCode = "E001", errorMessage = "Transaction not found")
        coEvery { mockDeleteTransactionsUseCase.invoke(txnIds) } returns DomainResult.Success(failureResult)

        // Act
        viewModel.handleEffect(sideEffect, onResult)

        // Allow coroutine to complete
        kotlinx.coroutines.delay(100)

        // Assert
//        coVerify { mockDeleteTransactionsUseCase.invoke(txnIds) }
//        verify { onResult(capture(onResultSlot)) }
//        val capturedEvent = onResultSlot.captured
//        assert(capturedEvent is DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed)
//        val failedEvent = capturedEvent as DeleteTransactionsReducer.ReducerViewEvent.RequestDeleteFailed
//        assertEquals("E001", failedEvent.errorCode)
//        assertEquals("Transaction not found", failedEvent.errorMessage)
    }

    @Test
    fun `handleEffect should handle RequestDeleteSuccess effect with noop`() {
        // Arrange
        val sideEffect = DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteSuccess
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        // Act - This should execute the else branch (noop)
        viewModel.handleEffect(sideEffect, onResult)

        // Assert - No exception should be thrown for noop case
        verify(exactly = 0) { onResult(any()) }
    }

    @Test
    fun `handleEffect should handle RequestDeleteFailed effect with noop`() {
        // Arrange
        val sideEffect =
            DeleteTransactionsReducer.ReducerViewEffect.RequestDeleteFailed("E001", "Error")
        val onResult: (DeleteTransactionsReducer.ReducerViewEvent) -> Unit = mockk(relaxed = true)

        // Act - This should execute the else branch (noop)
        viewModel.handleEffect(sideEffect, onResult)

        // Assert - No exception should be thrown for noop case
        verify(exactly = 0) { onResult(any()) }
    }

}
